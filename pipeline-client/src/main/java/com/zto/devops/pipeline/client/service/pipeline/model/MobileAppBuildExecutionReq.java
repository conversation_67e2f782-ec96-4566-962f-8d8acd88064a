package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

@Data
public class MobileAppBuildExecutionReq extends PageQueryBase implements Serializable {
    @ZsmpModelProperty(description = "版本编码", required = true)
    private String versionCode;
    @ZsmpModelProperty(description = "应用编码", required = true)
    private String applicationCode;
    @ZsmpModelProperty(description = "产品编码", required = true)
    private String productCode;
    @ZsmpModelProperty(description = "环境", required = false)
    private List<EnvEnum> env;
    @ZsmpModelProperty(description = "制品包类型", required = false)
    private List<String> artifactPackageType;
}