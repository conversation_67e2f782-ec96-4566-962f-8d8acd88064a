package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.pipeline.client.model.release.enums.ReleaseEventActionEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/10
 * @Version 1.0
 */
@Setter
@Getter
public class ExecuteReleasePlanReq implements Serializable {

    @GatewayModelProperty(description = "要执行的事件编码")
    private String eventCode;

    @GatewayModelProperty(description = "执行计划编码")
    private String versionCode;

    @GatewayModelProperty(description = "执行的操作")
    private ReleaseEventActionEnum action;
}
