package com.zto.devops.pipeline.client.service.application.model.strategy;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.devops.pipeline.client.model.application.entity.strategy.AppNetworkStrategyVO;
import lombok.*;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ListAppNetworkStrategyGroupResp implements Serializable {
    private static final long serialVersionUID = -5543709595623491929L;

    private List<AppNetworkStrategyVO> strategyList;

    private Set<EnvEnum> envs;

    public static ListAppNetworkStrategyGroupResp buildSelf(List<AppNetworkStrategyVO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return new ListAppNetworkStrategyGroupResp();
        }
        List<AppNetworkStrategyVO> uniqueList = list.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(AppNetworkStrategyVO::getDeduplicateCode))
                .values()
                .stream()
                .map(group -> group.get(0))
                .collect(Collectors.toList());
        return ListAppNetworkStrategyGroupResp.builder()
                .strategyList(uniqueList)
                .envs(list.stream().map(AppNetworkStrategyVO::getEnv).collect(Collectors.toSet()))
                .build();
    }
}
