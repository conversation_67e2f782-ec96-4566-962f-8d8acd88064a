package com.zto.devops.pipeline.client.service.application;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.application.entity.build.BuildVersionRegexVO;
import com.zto.devops.pipeline.client.service.application.model.BuildVersionRegexReq;
import com.zto.devops.pipeline.client.service.application.model.ReinforceStrategyReq;
import com.zto.devops.pipeline.client.service.checkItem.model.EnumResp;

import java.util.List;

/**
 * @Author：chenguangyu
 * @Description：
 * @Date：2025/6/30 14:01
 */
public interface IApplicationEnumService {

    /**
     * 查询工程类型
     */
    Result<List<EnumResp>> listBuildProjectType();
    /**
     * 查询Flutter版本
     */
    Result<List<EnumResp>> listFlutterVersion();
    /**
     * 查询版本号策略
     */
    Result<List<EnumResp>> listVersionPloy();
    /**
     * 查询导出类型
     */
    Result<List<EnumResp>> listBuildExportMethod();
    /**
     * 查询构建版本版本号策略
     */
    Result<List<EnumResp>> listCfBundlePloy();
    /**
     * 查询执行Pod
     */
    Result<List<EnumResp>> listPodInstall();
    /**
     * 查询加固策略
     */
    Result<List<EnumResp>> listReinforceStrategy(ReinforceStrategyReq req);
    /**
     * 查询升级方式
     */
    Result<List<EnumResp>> listUpgradeType();

    /**
     *  查询构建版本正则
     * */
    Result<List<BuildVersionRegexVO>> listBuildVersionRegex(BuildVersionRegexReq req);
    /**
     * 查询安装包类型枚举
     */
    Result<List<EnumResp>> listBuildOutPackageType();
}
