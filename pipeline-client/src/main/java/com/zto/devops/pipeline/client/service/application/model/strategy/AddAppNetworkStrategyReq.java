package com.zto.devops.pipeline.client.service.application.model.strategy;

import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.devops.pipeline.client.enums.application.DirectionTypeEnum;
import com.zto.devops.pipeline.client.enums.application.NetworkProtocolTypeEnum;
import com.zto.devops.pipeline.client.enums.application.NetworkStrategyTypeEnum;
import com.zto.devops.pipeline.client.model.application.entity.strategy.AppNetworkStrategyItemVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: chenguangyu
 * @Description:
 * @Date: 2024/7/11 11:26
 */
@Data
@ZsmpModel(description = "申请应用网络策略Req")
public class AddAppNetworkStrategyReq implements Serializable {

    @ZsmpModelProperty(description = "应用编码", sample = "1", required = true)
    private String applicationCode;

    @ZsmpModelProperty(description = "网络策略名称", sample = "网络策略名称", required = true)
    private String networkStrategyName;

    @ZsmpModelProperty(description = "网络策略类型。EGRESS(出口(Egress))", sample = "EGRESS", required = true)
    private DirectionTypeEnum directionType;

    @ZsmpModelProperty(description = "环境。PRO(生产环境/预发环境);FAT(测试环境/开发环境)", sample = "FAT", required = true)
    private List<EnvEnum> envs;

    @ZsmpModelProperty(description = "网络策略类型:DOMAIN(域名)、CIDR", sample = "DOMAIN", required = false)
    private NetworkStrategyTypeEnum networkStrategyType;

    @ZsmpModelProperty(description = "网络策略内容", sample = "baidu.com", required = false)
    private String networkStrategyContent;

    @ZsmpModelProperty(description = "端口号", sample = "80", required = false)
    private String port;

    @ZsmpModelProperty(description = "网络协议：TCP/UDP", sample = "TCP", required = false)
    private NetworkProtocolTypeEnum networkProtocol;

    @ZsmpModelProperty(description = "申请理由", sample = "申请理由", required = true)
    private String applyReason;

    @ZsmpModelProperty(description = "策略配置项", required = false)
    private List<AppNetworkStrategyItemVO> itemList;

}
