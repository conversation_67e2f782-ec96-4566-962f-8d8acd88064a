package com.zto.devops.pipeline.client.service.apiroute.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.apiroute.GatewayTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *网关域名模糊查询
 *
 * <AUTHOR>
 * @date 2024-10-23 16:56
 */
@Data
public class GatewayDomainInfoReq implements Serializable {
    @GatewayModelProperty(description = "产品code")
    @Auth(type = AuthTypeConstant.PRODUCT)
    private String productCode;

    /**
     * 域名，模糊匹配
     */
    @GatewayModelProperty(description = "域名模糊匹配",required = false)
    private String gatewayDomainName;

    @GatewayModelProperty(description = "网关类型",required = false)
    private GatewayTypeEnum gatewayType;
}
