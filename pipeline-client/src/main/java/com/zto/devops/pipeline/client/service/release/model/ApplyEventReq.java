package com.zto.devops.pipeline.client.service.release.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/11
 * @Version 1.0
 */
@Data
public class ApplyEventReq implements Serializable {

    @GatewayModelProperty(description = "事件的code")
    private List<String> codes;
    @GatewayModelProperty(description = "版本编码")
    private String versionCode;
    @GatewayModelProperty(description = "产品编码")
    private String productCode;
    @GatewayModelProperty(description = "指定应用到某个分组内", required = false)
    private String targetParent;
    @GatewayModelProperty(description = "是否需要新建分组，并移动到新的分组中", required = false)
    private Boolean newParentAndMoveIn = false;
}
