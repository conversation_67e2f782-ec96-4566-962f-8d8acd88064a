package com.zto.devops.pipeline.client.service.contingency.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @Date: 2025/3/31 16:35
 */

@Data
@ZsmpModel(description = "预案执行检查req")
public class PreExecuteContingencyPlanReq implements Serializable {

    @ZsmpModelProperty(description = "预案编码", required = true)
    private String contingencyPlanCode;
}
