package com.zto.devops.pipeline.client.service.release.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Description:
 * @Date: 2024/11/4 14:32
 */
@Data
public class FindWorkOrderReq implements Serializable {
    @GatewayModelProperty(description = "上线计划编码", required = true)
    private String releasePlanCode;
    @GatewayModelProperty(description = "事件编码", required = true)
    private String releaseEventCode;
}
