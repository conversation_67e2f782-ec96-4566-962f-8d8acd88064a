package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.GrayscaleStatusEnum;
import com.zto.devops.pipeline.client.enums.GrayscaleTypeEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: chenguangyu
 * @Description:
 * @Date: 2022/8/25 9:32
 */
@Data
@GatewayModel(description = "灰度分页查询req")
public class PageGrayscaleReq extends PageQueryBase implements Serializable {

    private static final long serialVersionUID = 6161870674742264104L;
    @Auth(type = AuthTypeConstant.PRODUCT)
    @ZsmpModelProperty(description = "产品编码", sample = "1", required = true)
    private String productCode;
    @ZsmpModelProperty(description = "环境", sample = "1", required = true)
    private String env;

    @ZsmpModelProperty(description = "发布时间-开始", sample = "1", required = false)
    private Date publishDateStart;
    @ZsmpModelProperty(description = "发布时间-结束", sample = "1", required = false)
    private Date publishDateEnd;

    @ZsmpModelProperty(description = "版本号(灰度目录)", sample = "1", required = false)
    private String directory;

    @ZsmpModelProperty(description = "应用信息", sample = "1", required = false)
    private String application;

    @ZsmpModelProperty(description = "应用codeList", sample = "1", required = false)
    private List<String> appCodes;

    @ZsmpModelProperty(description = "灰度状态", sample = "1", required = false)
    private List<GrayscaleStatusEnum> grayStatusList;

    @ZsmpModelProperty(description = "发布类型:灰度、全网", sample = "1", required = false)
    private List<GrayscaleTypeEnum> grayTypeList;

    @ZsmpModelProperty(description = "应用类型，区分移动端查询", sample = "1", required = false)
    private List< String> appTypeCodeList;

}
