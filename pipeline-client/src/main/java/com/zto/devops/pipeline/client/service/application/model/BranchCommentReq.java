package com.zto.devops.pipeline.client.service.application.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2022/9/13
 * @Version 1.0
 */
@Data
public class BranchCommentReq implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "流程编号")
    private String flowCode;

}
