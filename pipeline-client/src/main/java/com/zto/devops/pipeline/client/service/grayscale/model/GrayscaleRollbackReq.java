package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: cher
 * @Date: 2023/9/7 17:32
 */
@Data
@ZsmpModel(description = "集群回滚时灰度查询req")
public class GrayscaleRollbackReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @ZsmpModelProperty(description = "产品编码", sample = "1", required = true)
    private String productCode;
    @ZsmpModelProperty(description = "应用编码", sample = "1", required = true)
    private String applicationCode;
    @ZsmpModelProperty(description = "环境", sample = "1", required = true)
    private EnvEnum env;
    @ZsmpModelProperty(description = "实例编码", sample = "1", required = true)
    private String instanceCode;
}
