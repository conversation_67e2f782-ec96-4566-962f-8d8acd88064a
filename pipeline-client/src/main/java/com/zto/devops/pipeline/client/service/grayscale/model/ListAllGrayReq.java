package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * list all gray req
 *
 * <AUTHOR>
 * @date 2023-04-07 10:03
 */
@Data
public class ListAllGrayReq extends PageQueryBase implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 环境
     */
    private EnvEnum env;

    /**
     * 应用id
     */
    private String appId;
    private String productCode;
}
