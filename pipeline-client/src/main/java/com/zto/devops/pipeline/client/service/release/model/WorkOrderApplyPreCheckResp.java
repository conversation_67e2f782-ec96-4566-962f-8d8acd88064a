package com.zto.devops.pipeline.client.service.release.model;

import cn.hutool.core.date.DateUtil;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.pipeline.client.enums.FlowBusinessTypeEnum;
import com.zto.devops.pipeline.client.model.flow.entity.ReleaseWindowVO;
import com.zto.devops.pipeline.client.model.release.vo.apply.ApplyPreCheckResult;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/18
 */
@Data
public class WorkOrderApplyPreCheckResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 能否操作
     */
    private boolean canOperate;

    /**
     * 显示的信息
     */
    private String message;

    /**
     * 版本类型
     */
    private FlowBusinessTypeEnum flowType;

    /**
     * 检查列表
     */
    private List<ApplyPreCheckResult> list;

    /**
     * 创建工单的个数
     */
    private Long needCreateWorkOrderNum;

    /**
     * 特批信息
     */
    private ReleaseWindowVO releaseWindowVO;

    public Long getNeedCreateWorkOrderNum() {
        return CollectionUtil.isNotEmpty(list)
                ? list.stream().filter(ApplyPreCheckResult::isNeedCreateWorkOrder).count()
                : 0;
    }

    public boolean isCanOperate() {
        return CollectionUtil.isNotEmpty(list) && list.stream().allMatch(ApplyPreCheckResult::isCanOperate);
    }

    public void fillFlowType(FlowBusinessTypeEnum flowType) {
        this.flowType = flowType;
    }

    public String getMessage() {
        if (!isCanOperate()) {
            return "存在未校验通过的检查项，请调整后再进行提交审核";
        }
        String pre = "";
        if (releaseWindowVO != null) {
            pre = String.format("当前为<span style=\"color:red; font-weight:bold; \" title=\"%s-%s\">大促期间</span>,需进行" +
                    "<span style=\"color:red; font-weight:bold\">特批发布</span>。",
                DateUtil.formatDateTime(releaseWindowVO.getStartTime()),
                DateUtil.formatDateTime(releaseWindowVO.getEndTime()));
        }
        Long needCreateWorkOrderNum = getNeedCreateWorkOrderNum();
        if (needCreateWorkOrderNum != 0L) {
            return pre + String.format("本版本上线计划审核一共会发起 %d 个工单；「上线计划发布」工单审核通过后，会自动发起剩余的工单；", needCreateWorkOrderNum);
        }
        if (releaseWindowVO != null) {
            return pre;
        }
        String flowTypeName = "版本";
        if (flowType != null) {
            flowTypeName = flowType.hotfix() ? "紧急版本" : "常规版本";
        }
        return String.format("当前[%s]没有中间件变更需要发起工单，提交时<span style=\"color:red; font-weight:bold\">无需发起审批工单</span>", flowTypeName);
    }
}
