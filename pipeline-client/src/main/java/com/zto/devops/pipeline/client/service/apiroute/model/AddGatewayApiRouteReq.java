package com.zto.devops.pipeline.client.service.apiroute.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.apiroute.GatewayTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 新增api网关/流量网关路由配置
 *
 * <AUTHOR>
 * @date 2024-10-23 16:36
 */
@Data
public class AddGatewayApiRouteReq implements Serializable {
    @GatewayModelProperty(description = "产品code",required = false)
    private String apiRouteCode;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品code")
    private String productCode;

    @GatewayModelProperty(description = "网关namespace")
    private String gatewayNamespace;

    @GatewayModelProperty(description = "网关域名")
    private String gatewayDomainName;

    @GatewayModelProperty(description = "网关类型")
    private GatewayTypeEnum gatewayType;

    @GatewayModelProperty(description = "网关api名称")
    private List<String> gatewayApiNameList;

    @GatewayModelProperty(description = "路由空间code")
    private String namespaceCode;
}
