package com.zto.devops.pipeline.client.service.checkItem.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/7/30
 */
@ZsmpModel(description = "编辑检查项入参")
@Data
public class EditCheckItemReq extends AddCheckItemReq implements Serializable {

    @ZsmpModelProperty(description = "检查项编码", sample = "false", required = true)
    private String checkItemCode;
}
