package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2023/6/9 19:07
 */
@Data
@ZsmpModel(description = "入参")
public class DeployApplicationToFatReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "应用APPID", sample = "", required = true)
    private String appId;

    @ZsmpModelProperty(description = "分支名称，默认master", sample = "master", required = false)
    private String branchName = "master";

    /**
     * 是否需要部署
     */
    @ZsmpModelProperty(description = "是否需要部署", sample = "", required = true)
    private Boolean needDeploy;


    /**
     * 强制使用Jenkins特定JOB跑构建，用于测试构建脚本
     */
    private String jenkinsJobId;
}
