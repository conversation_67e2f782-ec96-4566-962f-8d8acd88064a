package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.pipeline.client.model.flow.entity.DeployStrategyVO;
import com.zto.devops.pipeline.client.model.release.enums.ReleaseEventType;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SaveDeployBatchReq implements Serializable {
    @ZsmpModelProperty(description = "批次信息", required = true)
    private List<DeployStrategyVO> deployStrategy;

    @ZsmpModelProperty(description = "产品编码", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "版本编码", required = true)
    private String versionCode;

    @ZsmpModelProperty(description = "事件类型", required = true)
    private ReleaseEventType eventType;

    @ZsmpModelProperty(description = "是否创建批次,true-创建批次,false-添加实例", required = true)
    private Boolean createBatch;
}
