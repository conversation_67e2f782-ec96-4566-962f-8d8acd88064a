package com.zto.devops.pipeline.client.constants;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * constant
 *
 * <AUTHOR>
 * @date 2023-04-07 09:36
 */
public class Constant {
    public static final String GATEWAY_SIGN = "luban.一站式研发平台签名";

    /**
     * 一周
     */
    public static final long ONE_WEEK = 7 * 24 * 60 * 60 * 1000L;

    /**
     * 日期格式化
     */
    public static final String TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * feature分支的prefix
     */
    public static final String FEATURE_BRANCH_PREFIX = "feature-";

    /**
     * hotfix分支的prefix
     */
    public static final String HOTFIX_BRANCH_PREFIX = "hotfix-";

    /**
     * release分支的prefix
     */
    public static final String RELEASE_BRANCH_PREFIX = "release-";

    /**
     * 核心应用实例前缀
     */
    public static final String CORE_INSTANCE_PREFIX = "core-";

    /**
     * 核心应用开启,调zke接口的uri
     */
    public static final String CORE_APPLICATION_OPEN_URI = "/api/v2/apps/1/sparing/on/%s";

    /**
     * 核心应用关闭，调zke接口的uri
     */
    public static final String CORE_APPLICATION_CLOSE_URI = "/api/v2/apps/1/sparing/off/%s";

    /**
     * 单实例复制核心实例的zke uri
     */
    public static final String CORE_INSTANCE_CREATE_URI = "/api/v2/apps/1/sparing/up/%s";

    public static final int ZKE_CLUSTER_NORMAL = 0;

    public static final int ZKE_CLUSTER_CORE = 1;

    public static final int ZKE_CLUSTER_CORE_BACK_UP = 2;

    /**
     * ZKE接口返回成功
     */
    public static final int ZKE_REQ_SUCCESS = 20000;

    /**
     * ZKE核心集群
     */
    public static final String ZKE_CORE_CLUSTER = "核心集群";

    /**
     * 域名后缀
     */
    public static final String AUTO_ENV_DOMAIN_SUFFIX_TEST = "dmetest.test.ztosys.com";

    public static final String AUTO_ENV_DOMAIN_SUFFIX_PRO = "dme.test.ztosys.com";
    /**
     * Jenkins pipeline job name
     */
    public static final String JENKINS_PIPELINE_JOB_NAME = "JENKINS-%s";

    /**
     * Jenkins pinpoint job name
     */
    public static final String JENKINS_PINPOINT_JOB_NAME = "JENKINS-PINPOINT-%s";

    public static final String JENKINS_PINPOINT_JOB_FILE_NAME = "pinpointPipelineFile.groovy";

    /**
     * pinpoint,代码无变动,不执行扫描
     */
    public static final String PINPOINT_NO_DIFF = "no_diff";

    public static final Set<String> GIT_EMAIL_EXCLUDE_LIST = new HashSet<>(Arrays.asList("<EMAIL>", "<EMAIL>"));

    public static final String GIT_BRANCH_MASTER = "master";

    public static final String ADMIN_AUTH_FAILED = "管理员认证失败";

    /**
     * 生产灾备环境,核心实例追加的jvm参数
     */
    public static final String JVM_ARG_CORE_INSTANCE_STANDBY_KEY = "jvm_arg_core_instance_standby";

    /**
     * 非灾备环境的核心实例
     */
    public static final String JVM_ARG_CORE_INSTANCE_KEY = "jvm_arg_core_instance";

    public static final String DOT = ".";

    public static final String DASH = "-";
    /**
     *启动参数Dtitans.dubbo.tag
     * */
    public static final String TITANS_DUBBO_TAG = " -Dtitans.dubbo.tag=";
    /**
     *空间标签存在时 添加启动参数 -Dzcat.agent.tag.namespaceTag=$空间标签
     * */
    public static final String ZCAT_AGENT_TAG = " -Dzcat.agent.tag.namespaceTag=";
    /**
     *启动参数 -Dtitans.env
     * */
    public static final String TITANS_ENV = " -Dtitans.env=";
    public static final String ENV_PRE = "pre";

    /**
     * jenkins workspace path
     */
    public static final String JENKINS_WORKSPACE_PATH_PREFIX = "/home/<USER>/agent/workspace/JENKINS-";

    public static final String DUBBO_CONSUMER_CHECK_KEY = "dubbo.consumer.check";
    public static final String DUBBO_REFERENCE_CHECK_KEY = "dubbo.reference.check";
    public static final String DUBBO_REGISTRY_CHECK_KEY = "dubbo.registry.check";

    public static final List<String> DUBBO_REFERENCE_CHECK_LIST = Arrays.asList(DUBBO_CONSUMER_CHECK_KEY, DUBBO_REFERENCE_CHECK_KEY, DUBBO_REGISTRY_CHECK_KEY);

    public static final String TARGET_EXCLUDE = "/target/";

    /**
     * 虚机上的jdk的默认版本
     */
    public static final String JDK_VERSION_DEFAULT = "jdk1.8.0_73";

    public static final String RELEASE_PLAN_APPLY_CHECK_PLACEHOLDER = "###";

    public static final String JOB = "job";

    public static final String ZMAS_BRIDGE_APP_PREFIX = "devops-gray-";

    public static final String APPLET_LOG_PRE = "APPLET_";

    public static final String H5_LOG_PRE = "H5_";

    public static final String APP_BUILD_CONFIG_FAT = "appBuildConfig_fat";
    public static final String APP_BUILD_CONFIG_PRO = "appBuildConfig_pro";
    public static final String APP_BUILD_CONFIG_ENV_DEBUG = "debug";
    public static final String APP_BUILD_CONFIG_ENV_RELEASE = "release";

    public static final String DISABLE_SEPARATE_PUBLISH_TIP = "此版本禁止独立发布，若需发布请进行版本合并或开启独立发布";

    public static final String MOBILE_EXT_FILE_DEFAULT_DOMAIN = "https://fscdn.zto.com";
}
