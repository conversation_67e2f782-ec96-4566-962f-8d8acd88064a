package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 核心应用列表查询请求
 *
 * <AUTHOR>
 * @date 2023-09-04 17:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CoreApplicationListReq extends PageQueryBase implements Serializable {
    /**
     * 产品code列表
     */
    private List<String> productCodeList;

    /**
     * 应用code列表
     */
    private List<String> appIdList;

    /**
     * 是否是核心应用
     */
    private List<Boolean> coreApplicationList;

    /**
     * 应用类型
     */
    private List<String> applicationTypeCodeList;
}
