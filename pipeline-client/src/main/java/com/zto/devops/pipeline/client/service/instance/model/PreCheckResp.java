package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/3/22
 * @Version 1.0
 */
@Data
public class PreCheckResp implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "检查是否通过")
    private Boolean pass;

    @GatewayModelProperty(description = "提示信息")
    private String message;
}
