package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.devops.pipeline.client.model.application.entity.build.BuildConfigVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author：chenguangyu
 * @Description：
 * @Date：2025/6/27 14:13
 */
@Data
@ZsmpModel(description = "修改应用构建配置信息req")
public class AppBuildConfigReq implements Serializable {

    private static final long serialVersionUID = 1L;
    @Auth(type = AuthTypeConstant.APPLICATION)
    @ZsmpModelProperty(description = "应用编码", required = true)
    private String code;

    @GatewayModelProperty(description = "环境", required = false)
    private String env;

    @ZsmpModelProperty(description = "构建参数配置", required = true)
    private BuildConfigVO buildConfig;
}
