package com.zto.devops.pipeline.client.service.pinpoint;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.service.pinpoint.model.*;

import java.util.List;

/**
 * @Author: renxinhui
 * @Description: Pinpoint相关服务
 * @Date: 2023/2/6 15:51
 */
public interface IPinpointService {

    /**
     * 缺陷等级汇总
     * */
    Result<BugSeveritySummary> bugSeveritySummary(BugSeveritySummaryReq req);

    /**
     * 缺陷分类汇总
     * */
    Result<List<BugTypeSummary>> listBugTypeSummary(ListBugTypeSummaryReq req);

    /**
     * 缺陷操作白名单权限
     *
     * @param req
     * @return
     */
    Result<BugWhitePermissionResp> bugWhitePermission(BugWhitePermissionReq req);

    /**
     * 添加缺陷白名单
     *
     * @param req
     * @return
     */
    Result<Void> addBugWhite(AddBugWhiteReq req);

    Result refreshProject(List<String> appIdList);

    Result syncBug(SyncBugReq req);

    Result deleteProject(List<String> appCodeList);

    Result<Void> triggerCodeScan(TriggerCodeScanReq req);

    /**
     * 代码扫描基础信息查询
     * @param req req
     *
     * @return PinpointTaskBaseInfoResp
     */
    Result<PinpointTaskBaseInfoResp> getPinpointTaskBaseInfo(PinpointTaskBaseInfoReq req);

    /**
     * 查询应用master分支扫描信息
     *
     * @param req
     * @return
     */
    Result<PinpointTaskBaseInfoResp> getApplicationMasterBranchLastPinpointTask(ApplicationMasterBranchLastPinpointTaskReq req);
}
