package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.devops.pipeline.client.model.cluster.entity.ApplicationRollbackLineVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RollbackPreviewResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @GatewayModelProperty( description = "应用回滚明细", sample = "[]")
    private List<ApplicationRollbackLineVO> applicationList;

//    @GatewayModelProperty( description = "可选制品列表", sample = "[]")
//    private List<ArtifactDetailVO> artifactList;
}
