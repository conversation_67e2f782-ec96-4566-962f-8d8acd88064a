package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.devops.pipeline.client.model.application.entity.ArtifactBuildVO;
import com.zto.devops.pipeline.client.model.pipeline.entity.PipelineExecutionNodeDO;
import com.zto.devops.pipeline.client.model.pipeline.entity.PipelineExecutionVO;
import lombok.Data;

import java.io.Serializable;

@Data
public class MobileAppBuildExecutionVO implements Serializable {

    private PipelineExecutionVO execution;

    private PipelineExecutionNodeDO buildStageNode;

    private PipelineExecutionNodeDO buildStepNode;

    private ArtifactBuildVO artifactBuildInfo;
}