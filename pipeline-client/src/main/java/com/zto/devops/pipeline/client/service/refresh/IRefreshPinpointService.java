package com.zto.devops.pipeline.client.service.refresh;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.service.refresh.model.AddPinpointCustomBugTypeSeverityReq;
import com.zto.devops.pipeline.client.service.refresh.model.DeletePinpointCustomBugTypeSeverityReq;

/**
 * @Author: cher
 * @Date: 2023/10/25 14:02
 **/
public interface IRefreshPinpointService {

    Result<Void> addPinpointCustomBugTypeSeverity(AddPinpointCustomBugTypeSeverityReq req);

    Result<Void> deletePinpointCustomBugTypeSeverity(DeletePinpointCustomBugTypeSeverityReq req);

}
