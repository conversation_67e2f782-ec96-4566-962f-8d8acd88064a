package com.zto.devops.pipeline.client.service.application.model;

import lombok.Data;

import java.io.Serializable;

/**
 * ZmasUploadSignResp
 *
 * <AUTHOR>
 * @date 2025-07-17 11:23
 */
@Data
public class ZmasUploadSignResp implements Serializable {
    /**
     * 生成的签名
     */
    private String sign;

    /**
     * 生成签名使用的时间戳
     */
    private String timestamp;

    /**
     * 生成签名使用的随机数
     */
    private String nonce;

    /**
     * dfs appid
     */
    private String dfsAppId;
}
