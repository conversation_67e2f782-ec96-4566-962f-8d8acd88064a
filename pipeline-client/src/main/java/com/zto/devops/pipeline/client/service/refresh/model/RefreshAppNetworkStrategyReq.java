package com.zto.devops.pipeline.client.service.refresh.model;

import com.zto.devops.pipeline.client.enums.application.NetworkStrategyStatusEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: renxinhui
 * @Description:
 * @Date: 2025/2/10 17:26
 */
@Data
@ZsmpModel(description = "刷新应用网络策略Req")
public class RefreshAppNetworkStrategyReq implements Serializable {
    @ZsmpModelProperty(description = "应用网络策略编码", sample = "1", required = true)
    private String appNetworkStrategyCode;
    @ZsmpModelProperty(description = "状态", sample = "申请理由", required = false)
    private NetworkStrategyStatusEnum status;
}
