package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.pipeline.client.enums.*;
import com.zto.devops.pipeline.client.model.instance.entity.WayneDetailVO;
import com.zto.devops.pipeline.client.model.instance.entity.ZCloudDetailVO;
import com.zto.devops.pipeline.client.model.simple.TagDO;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 实例详情出参
 * @Author: cher
 * @Date: 2021/8/30 18:57
 **/
@Data
@GatewayModel(description = "实例详情")
public class InstanceResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实例编码
     */
    private String code;

    /**
     * 实例名称
     */
    private String name;

    /**
     * 应用集群编码
     */
    private String clusterCode;

    /**
     * 空间编码
     */
    private String namespaceCode;

    /**
     * 应用编码
     */
    private String applicationCode;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * appId
     */
    private String appId;

    /**
     * 部署类型
     */
    private DeployTypeEnum deployType;

    /**
     * 部署参数是否变化(上次部署成功的与预计下次部署的是否一样)
     */
    private Boolean runningDeploymentParamChanged;

    /**
     * 部署参数(上次部署成功的)
     */
    private String runningDeploymentParam;

    /**
     * 部署参数(再次部署将会生效的，实时算出来的)
     */
    private String pendingDeploymentParam;

    /**
     * 部署参数
     */
    private String deploymentParam;

    /**
     * 默认部署参数(不可修改)
     */
    private String defaultDeploymentParam;

    /**
     * 部署路径
     */
    private String deployPath;

    /**
     * 主机id
     */
    private String hostId;

    /**
     * wayne实例id
     */
    private Long wayneDeploymentId;

    /**
     * 主机来源
     */
    private String computerSource;

    /**
     * 机房 : 湖州/无锡/吴兴/阿里云国际/阿里云金融
     */
    private ComputerRoomEnum computerRoom;

    /**
     * 机房 : 湖州/无锡/吴兴/阿里云国际/阿里云金融
     */
    private String computerRoomDesc;

    /**
     * 类型
     */
    private MachineTypeEnum type;

    /**
     * 类型 : VM/容器
     */
    private String typeDesc;

    /**
     * 类型的子类型
     */
    private GrayTagEnum grayTag;

    /**
     * 类型的子类型
     */
    private String grayTagDesc;

    /**
     * 主机ip
     */
    private String ip;

    /**
     * 服务端口
     */
    private String port;

    /**
     * 主机SaltMinionId
     */
    private String minionId;

    /**
     * 环境标签
     */
    private EnvEnum env;

    /**
     * 环境标签
     */
    private String envDesc;

    /**
     * 健康检查端口
     */
    private String healthyPort;

    /**
     * jacoco覆盖率检查端口
     */
    private String jacocoPort;

    /**
     * 最近一次部署人
     */
    private String deployer;

    /**
     * 最近一次部署时间
     */
    private Date deploymentTime;

    /**
     * 包仓库
     */
    private String commit;

    /**
     * 8位包仓库
     */
    private String shortCommit;

    /**
     * 包仓库url
     */
    private String commitUrl;

    /**
     * 制品code
     */
    private String artifactCode;

    /**
     * 制品名称
     */
    private String artifactName;

    /**
     * 制品构建时间
     */
    private Date artifactBuildTime;

    /**
     * 制品构建版本名称
     */
    private String flowName;

    /**
     * 实例状态
     */
    private InstanceStatusEnum status;

    /**
     * 实例状态
     */
    private String statusDesc;

    /**
     * wayne信息
     */
    private WayneDetailVO wayneDetail;

    /**
     * zCloud信息
     */
    private ZCloudDetailVO zCloudDetail;

    /**
     * 标签
     */
    private List<TagDO> tags;

    private String apolloSecret;

    /**
     * 是否是固定ip实例
     */
    private Boolean isFixedIp;

    /**
     * 固定ip实例是否存在审批单
     */
    private Boolean fixedIpInstanceAuditing;

    /**
     * 枚举类设置desc
     */
    public void setEnumDesc() {
        this.setStatusDesc(Objects.nonNull(this.status) ? this.status.getValue() : null);
        String subTypeDesc = Objects.nonNull(this.grayTag) ? this.grayTag.getValue() : null;
        if (MachineTypeEnum.FRONT_OSS == this.type && StringUtils.isNotBlank(subTypeDesc)) {
            this.setTypeDesc(this.type.getValue() + "-" + subTypeDesc);
        } else {
            this.setTypeDesc(Objects.nonNull(this.type) ? this.type.getValue() : null);
        }
        this.setGrayTagDesc(subTypeDesc);
        this.setEnvDesc(Objects.nonNull(this.env) ? this.env.getValue() : null);
        this.setComputerRoomDesc(Objects.nonNull(this.computerRoom) ? this.computerRoom.getValue() : null);
    }

    public void setShortCommit() {
        this.setShortCommit(Objects.nonNull(commit) && commit.length() > 0 ? commit.substring(0, 8) : "");
    }

    @GatewayModelProperty(description = "停止命令参数")
    private String stopParam;
}
