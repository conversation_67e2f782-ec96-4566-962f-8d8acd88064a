package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.DeployTypeEnum;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/9/1
 * @Version 1.0
 */
@Data
public class ContainerFormatReq implements Serializable {

    private static final long serialVersionUID = 1L;

     @GatewayModelProperty(description = "应用部署类型")
     private DeployTypeEnum deployType;

     @GatewayModelProperty(description = "环境")
     @NotNull(message = "环境不能为空")
     private EnvEnum env;

     @Auth(type = AuthTypeConstant.PRODUCT)
     @GatewayModelProperty(description = "产品code")
     @NotBlank(message = "产品编码不能为空")
     private String productCode;
}
