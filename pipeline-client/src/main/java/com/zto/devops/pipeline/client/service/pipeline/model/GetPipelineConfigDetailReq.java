package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@ZsmpModel(description = "流水线配置")
@Data
public class GetPipelineConfigDetailReq implements Serializable {

    @ZsmpModelProperty(description = "配置编号", required = true)
    private String configCode;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @ZsmpModelProperty(description = "产品编码", required = true )
    private String productCode;

}
