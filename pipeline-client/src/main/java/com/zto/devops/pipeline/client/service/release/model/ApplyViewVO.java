package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.pipeline.client.enums.NoticeResultEnum;
import com.zto.devops.pipeline.client.model.release.enums.CommonTypeEnum;
import com.zto.devops.pipeline.client.model.release.enums.WorkOrderExecStateEnum;
import com.zto.devops.pipeline.client.model.release.enums.WorkOrderStatusEnum;
import com.zto.devops.pipeline.client.model.release.enums.WorkOrderTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

@Getter
@Setter
public class ApplyViewVO implements Serializable {

    private Long id;

    private String name;

    private String productCode;

    private String productName;

    private String appId;

    private Long serverRoomId;

    private String serverRoomName;

    private String serverRoomNote;

    // 逻辑资源类型
    private WorkOrderTypeEnum resourceType;
    private String resourceDesc;

    // worker类型
    private CommonTypeEnum workerName;

    private String data;

    private String status;

    // 当前审核人
    private Collection<String> currentApplyUsers;

    private Boolean hasOptPermission;

    private Long envId;
    private String envName;

    private String applyTime;
    private String message;

    private String updater;

    private Boolean hideOperation = false;

    /**
     * current node status (which node is approving)
     *
     * @see #currentNodeDescription
     */
    private String customApproveStatus;
    private String customRefuseStatus;

    private String creator;

    /**
     * 是否可以撤回
     */
    private Boolean canRevoke;

    /**
     * 审批页面url
     */
    private String taskUrl;

    /**
     * 前一个节点描述
     */
    private String previousNodeDescription;

    /**
     * 当前审批节点节点描述
     */
    private String currentNodeDescription;

    private List<String> dfsfilenames;

    private String dfsFileProcessStatus;

    private String dfsFileProcessStatusDesc;

    // 一站式发布单id
    private String devopsOrderCode;

    // 工单是否是终态 -1-未知, 0-非终态, 1-终态, 2-中间态(比如db工单暂停)
    private Integer applyExecEndStatus = -1;

    // 工单执行结果状态 -1-未开始, 0-失败, 1-成功, 2-暂停  3-退回
    private Integer applyExecResultStatus = -1;

    //工单执行状态描述,用于展示
    private String applyExecStatusDesc;

    //    @ApiModelProperty("数据库工单id，zbase_manager_db_work_order.id 是数据库工单该字段才有值")
    private Long dbWorkerOrderId;
    //    @ApiModelProperty("数据库work工单id，zbase_manager_db_work_order.work_order_id 是数据库工单该字段才有值")
    private Long workOrderId;
    /**
     * 工单是否已执行完毕
     * @return
     */
    public boolean executed() {
        return applyExecEndStatus != null && applyExecEndStatus == 1;
    }

    /**
     * 获取工单执行终态状态。true:终态。非终态返回空
     * */
    public NoticeResultEnum getExecFinalState(){
        if(!executed()){
            return null;
        }
        if(Objects.isNull(applyExecResultStatus)){
            return null;
        }
        if(Objects.equals(applyExecResultStatus, 1)){
            return NoticeResultEnum.SUCCESS;
        }else if(Objects.equals(applyExecResultStatus, 0) || Objects.equals(applyExecResultStatus, 3)){
            return NoticeResultEnum.FAILED;
        }
        return null;
    }
    /**
     * 工单是否为终态
     * @return
     */
    public WorkOrderStatusEnum computeWorkOrderStatus() {
       return WorkOrderStatusEnum.convertApplyStatusEnum(status);
    }

    public WorkOrderExecStateEnum computeWorkOrderExecState() {
        return WorkOrderExecStateEnum.convertExecuteStatusEnum(applyExecResultStatus);
    }

    /**
     * 能否撤回工单
     * @return
     */
    public boolean canRevoke() {
        return computeWorkOrderStatus().processing();
    }

    /**
     * 能否 退回， 此时工单已审批结束
     * @return
     */
    public boolean canBackApply() {
        WorkOrderStatusEnum status = computeWorkOrderStatus();
        return status.passed() && computeWorkOrderExecState().canBackApply();
    }

    /**
     * 被拒绝，或被退回
     * @return
     */
    public boolean invalid() {
        WorkOrderStatusEnum workOrderStatus = computeWorkOrderStatus();
        if (workOrderStatus.invalid()) {
            return true;
        }
        WorkOrderExecStateEnum execState = computeWorkOrderExecState();
        return execState.backed();
    }

    public boolean finished() {
        return computeWorkOrderStatus().finished() && computeWorkOrderExecState().finished();
    }


    /**
     * 上线计划中能否复用该工单
     * @return
     */
    public boolean canReuseForReleasePlan() {
        WorkOrderStatusEnum workOrderStatus = computeWorkOrderStatus();
        if (workOrderStatus.processing()) {
            return true;
        }
        if (workOrderStatus.passed()) {
            WorkOrderExecStateEnum execState = computeWorkOrderExecState();
            return !execState.backed() && !execState.failExecuted();
        }
        return false;
    }

    public void setApplyExecStatusDesc(String applyExecStatusDesc) {
        this.applyExecStatusDesc = applyExecStatusDesc;
    }

    public String nodeAssignee() {
        if (backed()) {
            return getApplyExecStatusDesc();
        }
        if (StringUtil.isNotEmpty(applyExecStatusDesc) && !"-".equals(applyExecStatusDesc)) {
            return applyExecStatusDesc;
        }
        return currentNodeDescription;
    }

    public String getApplyExecStatusDesc() {
        if (backed()) {
            return "已退回";
        }
        return applyExecStatusDesc;
    }

    public boolean backed() {
        return applyExecResultStatus != null && applyExecResultStatus == 3;
    }

    public boolean applyToPro(List<String> proEnvs) {
        return CollectionUtil.isNotEmpty(proEnvs) && StringUtil.isNotEmpty(envName) && proEnvs.contains(envName);
    }
}
