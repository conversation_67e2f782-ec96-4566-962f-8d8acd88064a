package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.pipeline.client.model.release.enums.ReleaseEventType;
import com.zto.devops.pipeline.client.model.release.vo.TargetEventGroupVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/2/26
 * @Version 1.0
 */
@Data
public class ListTargetEventGroupResp implements Serializable {

    private String tip;

    private String action;

    private List<TargetEventGroupVO> groups;

    public void showTip(List<ReleaseEventType> eventTypes) {
        this.action = "SHOW_TIP";
        String groupNames = eventTypes.stream().map(ReleaseEventType::getEventName).collect(Collectors.joining(","));
        tip = "【 " + groupNames + " 】类型存在多个步骤，将自动应用到对应类型的最后一个步骤";
    }

    public void chooseGroup() {
        this.action = "CHOOSE_GROUP";
    }

    public void buildTip() {
        if (CollectionUtil.isEmpty(groups)) {
            return;
        }
        Optional<TargetEventGroupVO> first = groups.stream().findFirst();
        if (!first.isPresent()) {
            return;
        }
        if (first.get().getEventType() == null) {
            return;
        }
        tip = "【" + first.get().getEventType().getEventName() + "】类型存在多个步骤，请选择要应用到的步骤";
    }

    public void directApply() {
        this.action = "DIRECT_APPLY";
    }
}
