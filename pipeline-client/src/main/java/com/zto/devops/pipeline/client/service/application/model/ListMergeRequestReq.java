package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2022/9/13
 * @Version 1.0
 */
@Data
public class ListMergeRequestReq implements Serializable {

    private static final long serialVersionUID = 1L;


   private List<String> branchName;

   @Auth(type = AuthTypeConstant.APPLICATION_LIST)
   private List<String> applicationCode;
}
