package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2021/11/1
 */
@Data
@GatewayModel(description = "实例空间code实体")
public class InstanceNamespaceReq implements Serializable {

    private static final long serialVersionUID = 1L;
    @Auth(type = AuthTypeConstant.INSTANCE)
    @GatewayModelProperty(description = "实例编码")
    private String instanceCode;

    @GatewayModelProperty(description = "空间编码")
    private String namespaceCode;

    @GatewayModelProperty(description = "门户固定机器标签", required = false)
    private String grayTag;
}
