package com.zto.devops.pipeline.client.service.application.model.strategy;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "同批次应用网络策略列表查询Req")
public class ListAppNetworkStrategyGroupReq implements Serializable {
    private static final long serialVersionUID = -7108770376942964017L;

    @ZsmpModelProperty(description = "应用网络策略编码", sample = "1", required = true)
    private String appNetworkStrategyCode;
}
