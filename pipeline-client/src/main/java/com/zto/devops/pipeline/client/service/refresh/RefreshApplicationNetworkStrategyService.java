package com.zto.devops.pipeline.client.service.refresh;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.service.refresh.model.RefreshAppNetworkStrategyReq;

/**
 * @Author: renxinhui
 * @Date: 2024/2/10 14:02
 **/
public interface RefreshApplicationNetworkStrategyService {

    /**
     * 刷新网络策略
     *
     * @return
     */
    Result<Void> refreshAppNetworkStrategy(RefreshAppNetworkStrategyReq req);
}
