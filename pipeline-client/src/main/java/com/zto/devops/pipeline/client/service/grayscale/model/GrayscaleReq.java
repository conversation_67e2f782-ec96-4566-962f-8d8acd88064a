package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.GrayscaleTypeEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: ch<PERSON><PERSON>yu
 * @Description:
 * @Date: 2022/8/25 9:40
 */
@Data
@GatewayModel(description = "灰度req")
public class GrayscaleReq implements Serializable {

    private static final long serialVersionUID = 2406544593899560914L;

    @Auth(type = AuthTypeConstant.GRAY_SCALE)
    @ZsmpModelProperty(description = "灰度编码code", sample = "1", required = true)
    private String code;

    @ZsmpModelProperty(description = "描述", sample = "1", required = false)
    private String grayscaleDescribe;

    @ZsmpModelProperty(description = "发布类型:灰度、全网", sample = "1", required = true)
    private GrayscaleTypeEnum type;

    @ZsmpModelProperty(description = "百分比", sample = "1", required = false)
    private BigDecimal ratio;

    @ZsmpModelProperty(description = "灰度规则", sample = "1", required = false)
    private String rules;

    @ZsmpModelProperty(description = "前端展示数据", sample = "1", required = false)
    private String uiData;
}
