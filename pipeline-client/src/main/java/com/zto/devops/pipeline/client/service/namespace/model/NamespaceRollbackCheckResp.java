package com.zto.devops.pipeline.client.service.namespace.model;

import com.zto.devops.pipeline.client.model.instance.entity.InstanceVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * namespace rollback check resp
 *
 * <AUTHOR>
 * @date 2023-03-17 16:19
 */
@Data
public class NamespaceRollbackCheckResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "回滚校验结果", sample = "true")
    private Boolean checkResult;

    @GatewayModelProperty(description = "回滚校验限制信息", required = false)
    private String checkLimitMsg;

    @GatewayModelProperty(description = "空间下实例信息", sample = "[]")
    private List<InstanceVO> instances;
}
