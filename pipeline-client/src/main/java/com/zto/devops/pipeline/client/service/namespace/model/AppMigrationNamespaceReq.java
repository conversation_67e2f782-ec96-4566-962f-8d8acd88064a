package com.zto.devops.pipeline.client.service.namespace.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * app migrate namespace req
 *
 * <AUTHOR>
 * @date 2023-03-16 13:19
 */
@Data
public class AppMigrationNamespaceReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "应用编码")
    private String applicationCode;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "当前产品code")
    @NotBlank(message = "当前产品code不能为空")
    private String productCode;

    @GatewayModelProperty(description = "环境" ,required = false)
    private String env;
}
