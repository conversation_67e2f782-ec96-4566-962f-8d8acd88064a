package com.zto.devops.pipeline.client.service.configset.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@GatewayModel(description = "key列表的req")
public class ListKeyReq implements Serializable {

    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "应用编码")
    private String applicationCode;

    @GatewayModelProperty(description = "环境。开发:DEV,测试:FAT,预发:PRE,生产:PRO",sample = "开发:DEV,测试:FAT,预发:PRE,生产:PRO")
    private EnvEnum env;

    @GatewayModelProperty(description = "key或者描述的模糊搜索",required = false)
    private String search;

}
