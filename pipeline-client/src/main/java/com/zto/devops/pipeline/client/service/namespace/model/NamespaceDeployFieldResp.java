package com.zto.devops.pipeline.client.service.namespace.model;

import com.zto.devops.pipeline.client.model.cluster.entity.NamespaceDeployPermissionsVO;
import com.zto.devops.pipeline.client.model.cluster.entity.NamespaceDeployScopeVO;
import com.zto.devops.pipeline.client.model.cluster.entity.NamespaceEditFieldPermissionsVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Set;

/**
 * namespace deploy field resp
 *
 * <AUTHOR>
 * @date 2023-03-16 10:37
 */
@Getter
@Setter
public class NamespaceDeployFieldResp implements Serializable {
    @GatewayModelProperty(description = "部署范围", required = false)
    private NamespaceDeployScopeVO deployScope;
    @GatewayModelProperty(description = "部署权限", required = false)
    private NamespaceDeployPermissionsVO deployPermissions;
    @GatewayModelProperty(description = "字段编辑权限", required = false)
    private Set<NamespaceEditFieldPermissionsVO> fieldPermissions;
}