package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @Description: 删除实例
 * @Author: cher
 * @Date: 2021/7/9 13:37
 **/
@Data
@GatewayModel(description = "删除实例实体")
public class RemoveInstanceReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实例编码
     */
    @Auth(type = AuthTypeConstant.INSTANCE)
    @GatewayModelProperty(description = "实例编码")
    @NotBlank(message = "实例编码不能为空")
    private String code;
}
