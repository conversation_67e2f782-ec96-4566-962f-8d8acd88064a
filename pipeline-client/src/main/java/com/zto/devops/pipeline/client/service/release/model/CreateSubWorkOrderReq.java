package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/17
 * @Version 1.0
 */
@ZsmpModel(description = "创建上线计划子工单")
@Data
public class CreateSubWorkOrderReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "版本编码", required = true)
    private String versionCode;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @ZsmpModelProperty(description = "应用code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "事件code", required = true)
    private String eventCode;

    @ZsmpModelProperty(description = "工单标题", required = false)
    private String title;

    @ZsmpModelProperty(description = "工单执行方式,1:开发执行 2:自动执行 3:DBA执行", required = false)
    private Integer executeType;
}
