package com.zto.devops.pipeline.client.service.checkItem.model;

import com.zto.devops.pipeline.client.enums.ProductRoleEnum;
import com.zto.devops.pipeline.client.enums.flow.CheckActionEnum;
import com.zto.devops.pipeline.client.enums.flow.CheckItemTypeEnum;
import com.zto.devops.pipeline.client.enums.flow.CheckRecordTypeEnum;
import com.zto.devops.pipeline.client.enums.flow.CheckTypeEnum;
import com.zto.devops.pipeline.client.model.checkItem.CheckItemTimeLevel;
import com.zto.devops.pipeline.client.model.checkItem.CheckScope;
import com.zto.devops.pipeline.client.model.checkItem.entity.CheckFailedNoticeAddress;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/30
 */
@Data
public class CheckItemDetailResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检查项编码
     */
    private String checkItemCode;

    /**
     * 检查项名称
     */
    private String checkItemName;

    /**
     * 类型
     */
    private CheckItemTypeEnum itemType;

    /**
     * 类型
     */
    private String itemTypeDesc;

    /**
     * 事项类型
     */
    private CheckRecordTypeEnum recordType;

    /**
     * 事项类型
     */
    private String recordTypeDesc;

    /**
     * 提出人编码
     */
    private Long introducerId;

    /**
     * 提出人
     */
    private String introducer;

    /**
     * 事项接受角色
     */
    private ProductRoleEnum relateRole;

    /**
     * 事项接受角色
     */
    private String relateRoleDesc;

    /**
     * 成功标题
     */
    private String successTitle;

    /**
     * 失败标题
     */
    private String failTitle;

    /**
     * 检查规则
     */
    private CheckTypeEnum checkRule;

    /**
     * 检查规则
     */
    private String checkRuleDesc;

    /**
     * 检查参数
     */
    private String checkRuleContext;

    /**
     * 触发时机
     */
    private CheckActionEnum checkTriggerAction;

    /**
     * 触发时机
     */
    private String checkTriggerActionDesc;

    /**
     * 状态：启用、禁用
     */
    private Boolean status;

    /**
     * 检查项描述
     */
    private String checkItemDesc;
    /**
     * 正向作用域
     */
    private List<CheckScope> forwardScopes;

    /**
     * 反向作用域
     */
    private List<CheckScope> reverseScopes;

    /**
     * 检查项目时间与等级关系
     */
    private List<CheckItemTimeLevel> timeLevels;

    /**
     * 检查失败通知群地址
     */
    private List<CheckFailedNoticeAddress> checkFailedNoticeAddresses;
}
