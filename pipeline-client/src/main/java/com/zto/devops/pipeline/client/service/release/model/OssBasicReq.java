package com.zto.devops.pipeline.client.service.release.model;

import com.zto.doc.annotation.ZModel;
import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
@ZModel(description = "oss基础接口入参")
public class OssBasicReq implements Serializable {

    private static final long serialVersionUID = 1870509636783316888L;

    @ZModelProperty(description = "文件唯一key", sample = "12918182818218",required = true)
    private String remoteFileId;

    @ZModelProperty(description = "桶的名称", sample = "bucketName",required = true)
    private String bucketName;

    @ZModelProperty(description = "文件名", sample = "beijin.png")
    private String fileName;

    @ZModelProperty(description = "生产环境cookie", sample = "xxx")
    private String proCookie;

    @ZModelProperty(description = "测试环境cookie", sample = "xxx")
    private String fatCookie;
}
