package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.model.cluster.entity.NamespaceRollbackLineVO;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RollbackSpecifiedNamespaceReq implements Serializable {
    private static final long serialVersionUID = 1L;
    @Auth(type = AuthTypeConstant.NAMESPACE)
    @GatewayModelProperty( description = "当前空间编码", sample = "NS202101010001")
    private String currentNamespaceCode;
    @GatewayModelProperty( description = "回滚空间编码", sample = "NS202101010002")
    private String rollbackNamespaceCode;
    @GatewayModelProperty( description = "回滚原因", sample = "回滚描述")
    private String description;
    @GatewayModelProperty( description = "回滚列表", sample = "[]")
    private List<NamespaceRollbackLineVO> rollbackLines;

    @GatewayModelProperty( description = "需下线的灰度编码", sample = "[]")
    private Set<String> grayscaleCodes;

    @ZsmpModelProperty(description = "需下线的小程序版本id", sample = "[]")
    private Set<Long> versionIds;

}
