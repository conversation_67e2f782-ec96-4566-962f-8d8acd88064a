package com.zto.devops.pipeline.client.service.release.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/10/23
 */
@Data
public class ReleasePlanWorkOrderParamsResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 版本编码
     */
    private String versionCode;

    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 申请人
     */
    private String creator;

    /**
     * 申请时间
     */
    private Date time;

    /**
     * 是否已撤销
     */
    private boolean undoFlag;
}
