package com.zto.devops.pipeline.client.service.application.model;


import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@ZsmpModel(description = "刷新应用信息")
@Data
public class RefreshApplicationReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "apps")
    private List<RefreshApplication> apps;

    @Data
    public static class RefreshApplication implements Serializable {
        private static final long serialVersionUID = 1L;

        @ZsmpModelProperty(description = "APPID")
        private String appId;
        @ZsmpModelProperty(description = "白名单原因")
        private String whiteListReason;
    }
}
