package com.zto.devops.pipeline.client.service.application.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 2022/12/19
 */
@Data
@ZsmpModel(description = "批量删除应用入参")
public class DeleteApplicationBatchReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 应用appId
     */
    @ZsmpModelProperty(description = "应用appId集合", required = true)
    private List<String> appIds;

    @ZsmpModelProperty(description = "删除的页数，每次最多20个", required = true)
    private Integer page;
}
