package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2023/6/9 19:07
 */
@Data
@ZsmpModel(description = "入参")
public class RestartApplicationToFatReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "应用APPID", sample = "", required = true)
    private String appId;
}
