package com.zto.devops.pipeline.client.service.apiroute.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.OperateEnum;
import com.zto.devops.pipeline.client.enums.OperateTypeEnum;
import com.zto.devops.pipeline.client.model.apiroute.entity.ApiRouteVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ManageApiRouteReq implements Serializable {

    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品code")
    private String productCode;

    @GatewayModelProperty(description = "路由空间code",required = false)
    private String namespaceCode;

    @GatewayModelProperty(description = "操作类型")
    private OperateEnum operateType;

    @GatewayModelProperty(description = "待处理路由的api列表",sample = "", required = false)
    private List<ApiRouteVO> apiRouteList;

}
