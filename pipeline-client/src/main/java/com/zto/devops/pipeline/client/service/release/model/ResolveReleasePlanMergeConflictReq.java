package com.zto.devops.pipeline.client.service.release.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/17
 * @Version 1.0
 */
@Data
public class ResolveReleasePlanMergeConflictReq implements Serializable {

    @GatewayModelProperty(description = "事件的code")
    private List<String> codes;
    @GatewayModelProperty(description = "版本编码")
    private String versionCode;
    @GatewayModelProperty(description = "产品编码")
    private String productCode;
}
