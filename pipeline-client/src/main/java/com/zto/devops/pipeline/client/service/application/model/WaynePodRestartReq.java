package com.zto.devops.pipeline.client.service.application.model;


import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class WaynePodRestartReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.INSTANCE)
    @ZsmpModelProperty(description = "实例编码", required = true)
    private String instanceCode;


    @ZsmpModelProperty(description = "集群名", required = true)
    private String clusterName;

    /**
     * 实例名
     */

    @ZsmpModelProperty(description = "空间", required = true)
    private String namespace;

    /**
     * pod名称
     */
    @ZsmpModelProperty(description = "pod名称", required = true)
    private String podName;

}
