package com.zto.devops.pipeline.client.service.application.model;


import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class WaynePodStatusReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 集群名
     */
    @GatewayModelProperty(description = "集群名")
    private String clusterName;

    /**
     * 实例名
     */
//    @Auth(type = AuthTypeConstant.INSTANCE_NAME)
    @GatewayModelProperty(description = "实例名")
    private String deploymentName;

    /**
     * pod名称
     */
    @GatewayModelProperty(description = "pod名称")
    private String podName;

}
