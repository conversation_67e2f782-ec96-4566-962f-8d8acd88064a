package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.pipeline.client.enums.GrayscaleTypeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * H5GrayscaleReq
 *
 * <AUTHOR>
 * @date 2025-06-05 10:04
 */
@Data
@ZsmpModel(description = "H5灰度req")
public class H5GrayscaleReq implements Serializable {
    @ZsmpModelProperty(description = "主键", sample = "1", required = true)
    private Long id;

    @ZsmpModelProperty(description = "描述", sample = "1", required = false)
    private String grayscaleDescribe;

    @ZsmpModelProperty(description = "发布类型:灰度、全网", sample = "1", required = true)
    private GrayscaleTypeEnum type;

    @ZsmpModelProperty(description = "百分比", sample = "1", required = false)
    private BigDecimal ratio;

    @ZsmpModelProperty(description = "灰度规则", sample = "1", required = false)
    private String rules;

    @ZsmpModelProperty(description = "前端展示数据", sample = "1", required = false)
    private String uiData;
}
