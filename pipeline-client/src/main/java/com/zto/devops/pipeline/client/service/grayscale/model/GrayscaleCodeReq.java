package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: ch<PERSON><PERSON>yu
 * @Description:
 * @Date: 2022/8/25 9:40
 */
@Data
@GatewayModel(description = "灰度req")
public class GrayscaleCodeReq implements Serializable {

    private static final long serialVersionUID = 2406544593899560914L;

    @Auth(type = AuthTypeConstant.GRAY_SCALE)
    @ZsmpModelProperty(description = "灰度编码code", sample = "1", required = true)
    private String code;
}
