package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.pipeline.client.model.release.enums.ReleaseEventType;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class EditReleaseEventReq implements Serializable {

    @GatewayModelProperty(description = "编码")
    private String idCode;

    @GatewayModelProperty(description = "标题")
    private String name;

    @GatewayModelProperty(description = "正文")
    private Object eventPayload;

    @GatewayModelProperty(description = "事件类型")
    private ReleaseEventType eventType;

    @GatewayModelProperty(description = "版本code")
    private String versionCode;
}
