package com.zto.devops.pipeline.client.service.release.model;


import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ListReleaseEventReq implements Serializable {
    @GatewayModelProperty(description = "上线计划版本编码")
    private String versionCode;
    @GatewayModelProperty(description = "上线计划序环境",required = false)
    private String env;
    @GatewayModelProperty(description = "是否过滤没有子节点的虚拟节点", required = false)
    private boolean emptyFilter = false;
}
