package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.model.cluster.entity.WayneClusterVO;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/2
 */
@Data
public class ExpandProPreCheckReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.APP_ID)
    @ZsmpModelProperty(description = "appId", required = true)
    private String appId;

    @ZsmpModelProperty(description = "空间编码", required = true)
    private String namespaceCode;

    /**
     * 实例编码
     */
    @ZsmpModelProperty(description = "实例编码")
    private String code;

    /**
     * 集群信息
     */
    @ZsmpModelProperty(description = "集群信息")
    private List<WayneClusterVO> clusters;
}
