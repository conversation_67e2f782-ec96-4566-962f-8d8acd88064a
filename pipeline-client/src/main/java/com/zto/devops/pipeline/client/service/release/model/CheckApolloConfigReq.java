package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.pipeline.client.model.rpc.middleware.GlobalParams;
import lombok.Data;

import java.io.Serializable;

@Data
public class CheckApolloConfigReq implements Serializable {
    private String clusterName;
    private String namespaceName;
    private String configText;
    private String appId;
    private GlobalParams pathParams;
}
