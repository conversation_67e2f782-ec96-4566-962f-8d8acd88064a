package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 特批产品列表出参
 * @Author: cher
 * @Date: 2022/9/26 16:16
 **/
@ZsmpModel(description = "特批产品列表入参")
@Data
public class SpecialProductListReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "对象名称", sample = "PRO_1")
    private String targetName;
}
