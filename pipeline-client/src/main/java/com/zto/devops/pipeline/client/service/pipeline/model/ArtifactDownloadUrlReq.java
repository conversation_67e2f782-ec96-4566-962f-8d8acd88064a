package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * artifact download url req
 *
 * <AUTHOR>
 * @date 2023-04-07 09:23
 */
@Data
@GatewayModel(description = "制品查询参数")
public class ArtifactDownloadUrlReq implements Serializable {

    private static final long serialVersionUID = 5525694890621214074L;

    @GatewayModelProperty(description = "包名称")
    private String packageName;
}
