package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.pipeline.client.model.release.enums.ReleaseEventType;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/13
 * @Version 1.0
 */
@Data
public class ReleasePlanMergeConflictDetail implements Serializable {

    private Boolean hasConflict;

    /**
     *  配置冲突问题
     * */
    private List<ApolloConflictVO> apolloConflicts;

    public Boolean getHasConflict() {
        return CollectionUtil.isNotEmpty(apolloConflicts);
    }

    public void fillConflict(ReleaseEventType eventType, List<EventConflictVO> conflicts) {
        switch (eventType) {
            case APOLLO_CONFIG_ITEM:
                setApolloConflicts(castConflicts(conflicts, ApolloConflictVO.class));
                break;
        }
    }

    private  <T> List<T> castConflicts(List<EventConflictVO> conflicts, Class<T> clazz) {
        if (CollectionUtil.isEmpty(conflicts)) {
            return Collections.emptyList();
        }
        return conflicts.stream()
                .filter(clazz::isInstance)
                .map(clazz::cast)
                .collect(Collectors.toList());
    }
}
