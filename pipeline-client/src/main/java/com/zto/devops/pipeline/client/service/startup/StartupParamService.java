package com.zto.devops.pipeline.client.service.startup;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.common.vo.EnumRelateVO;
import com.zto.devops.pipeline.client.service.startup.model.*;

import java.util.List;

/**
 * startup param service
 *
 * <AUTHOR>
 * @date 2025-04-10 15:49
 */
public interface StartupParamService {

    /**
     * 新增启动参数匹配规则
     * @param req req
     * @return void
     */
    Result<Void> addStartupParamRule(AddStartupParamRuleReq req);

    /**
     * 启动参数规则列表查询
     * @return resp
     */
    Result<QueryStartupParamRuleResp> queryStartupParamRule(QueryStartupParamRuleReq req);

    /**
     * 编辑启动参数规则
     * @param req req
     * @return void
     */
    Result<Void> editStartupParamRule(EditStartupParamRuleReq req);

    /**
     * 修改规则状态
     * @param req req
     * @return void
     */
    Result<Void> statusChangeStartupParamRule(StatusChangeStartupParamRuleReq req);

    /**
     * 删除启动参数规则
     * @param req req
     * @return void
     */
    Result<Void> deleteStartupParamRule(DeleteStartupParamRuleReq req);

    /**
     * 启动参数规则，条件的所有枚举值
     * @return resp
     */
    Result<List<EnumRelateVO>> conditionEnumList();

    /**
     * 启动参数规则，条件的操作符的所有枚举值
     * @return resp
     */
    Result<List<EnumRelateVO>> conditionEnumOperationList();

    /**
     * 新老启动参数比对
     * @return void
     */
    Result<Void> startupParamDiffCheck(DiffCheckStartupParamReq req);


}
