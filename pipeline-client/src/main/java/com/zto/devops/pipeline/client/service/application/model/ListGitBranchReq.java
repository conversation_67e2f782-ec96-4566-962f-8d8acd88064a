package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * list git branch req
 *
 * <AUTHOR>
 * @date 2025-06-30 15:06
 */
@Data
public class ListGitBranchReq extends PageQueryBase implements Serializable {
    @GatewayModelProperty(description = "git project url", sample = "https://gitlab.zto.com/zmas/zmas-front-end.git")
    private String gitProjectUrl;

    @GatewayModelProperty(description = "search key", sample = "feature-",required = false)
    private String searchKey;
}
