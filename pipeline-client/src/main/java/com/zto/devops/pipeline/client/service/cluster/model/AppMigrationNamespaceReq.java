package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @Description: 迁移应用获取空间
 * @Author: cher
 * @Date: 2021/12/6 16:01
 **/
@Data
public class AppMigrationNamespaceReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "应用编码" ,required = false)
    private String applicationCode;

    @GatewayModelProperty(description = "当前产品code")
    @NotBlank(message = "当前产品code不能为空")
    private String productCode;

    @GatewayModelProperty(description = "环境" ,required = false)
    private String env;
}
