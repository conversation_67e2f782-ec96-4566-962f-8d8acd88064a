package com.zto.devops.pipeline.client.service.release;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.pipeline.client.model.release.vo.AppMrVO;
import com.zto.devops.pipeline.client.model.release.vo.ReleaseEventVO;
import com.zto.devops.pipeline.client.model.release.vo.ReleasePlanVO;
import com.zto.devops.pipeline.client.model.rpc.middleware.ApplyAuditDTO;
import com.zto.devops.pipeline.client.model.workorder.vo.WorkOrderVO;
import com.zto.devops.pipeline.client.service.flow.model.FlowAuditGetReq;
import com.zto.devops.pipeline.client.service.release.model.*;
import com.zto.devops.pipeline.client.service.workorder.model.FindWorkOrderReq;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 外网访问的接口
 * @Date 2025/1/8
 * @Version 1.0
 */
public interface ExternalApiService {

    Result<List<ReleaseEventVO>> listReleaseEvents(ListReleaseEventReq req);

    Result<ReleasePlanVO> findReleasePlan(FindReleasePlanReq req);

    Result<ReleasePlanWorkOrderParamsResp> queryWorkOrderParams(FlowAuditGetReq req);


    /**
     * 查询版本下应用的mr有效评论
     * @param req
     * @return
     */
    Result<AppMrVO> listAppMrComment(ListAppMrCommentReq req);

    /**
     * 查询版本下mr有效评论的评论人列表
     * @param req
     * @return
     */
    Result<List<User>> listMrCommentators(ListAppMrCommentatorReq req);

    Result<WorkOrderVO> findWorkOrder(FindWorkOrderReq req);

    Result<String> simpleAudit(ApplyAuditDTO req);

    Result<ApplyPreCheckResp> releasePlanPreCheck(ReleasePreCheckReq req);
}
