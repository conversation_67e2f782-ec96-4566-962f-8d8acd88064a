package com.zto.devops.pipeline.client.service.external.model;

import com.zto.devops.pipeline.client.enums.flow.CheckItemTypeEnum;
import com.zto.devops.pipeline.client.model.rpc.product.ProductMemberVO;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/17
 */
@Data
public class CheckItemRecordResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * appId
     */
    @ZsmpModelProperty(description = "appId")
    private String appId;

    /**
     * 检查类型：
     * UNKNOWN("未知类型，需要兼容"),
     CHECK("检查"),
     IMPROVE("改善"),
     PERSISTENT_IMPROVE("持续改善")
     */
    @ZsmpModelProperty(description = "检查类型")
    private CheckItemTypeEnum itemType;

    /**
     * 检查类型描述
     * */
    @ZsmpModelProperty(description = "检查类型描述")
    private String itemTypeDesc;

    /**
     * 产品编码
     */
    @ZsmpModelProperty(description = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ZsmpModelProperty(description = "产品名称")
    private String productName;

    /**
     * 部门ID
     */
    @ZsmpModelProperty(description = "部门ID")
    private Long deptId;

    /**
     * 部门名称
     */
    @ZsmpModelProperty(description = "部门名称")
    private String deptName;

    /**
     * 版本名称
     */
    @ZsmpModelProperty(description = "版本名称")
    private String versionName;

    /**
     * 创建时间
     */
    @ZsmpModelProperty(description = "创建时间")
    private Date gmtCreate;

    /**
     * 创建人ID
     */
    @ZsmpModelProperty(description = "创建人ID")
    private Long creatorId;

    /**
     * 创建人
     */
    @ZsmpModelProperty(description = "创建人")
    private String creator;

    /**
     * 第一告警人
     */
    @ZsmpModelProperty(description = "第一告警人ID")
    private Long firstAlerterId;
    /**
     * 第一告警人名称
     */
    @ZsmpModelProperty(description = "第一告警人名称")
    private String firstAlerterName;

    /**
     * 第二告警人
     */
    @ZsmpModelProperty(description = "第二告警人ID")
    private Long secondAlerterId;
    /**
     * 第二告警人名称
     */
    @ZsmpModelProperty(description = "第二告警人名称")
    private String secondAlerterName;

    /**
     * 架构师
     */
    @ZsmpModelProperty(description = "架构师列表")
    private List<ProductMemberVO> architectList;

    /**
     * 检查结果：true-成功，false-失败
     */
    @ZsmpModelProperty(description = "检查结果：true-成功，false-失败")
    private Boolean checkResult;

    @ZsmpModelProperty(description = "流程编码")
    private String flowCode;

    @ZsmpModelProperty(description = "检查项编码")
    private String checkItemCode;
}
