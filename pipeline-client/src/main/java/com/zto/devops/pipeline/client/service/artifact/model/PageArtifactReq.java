package com.zto.devops.pipeline.client.service.artifact.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PageArtifactReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 1L;
    @GatewayModelProperty(description = "commitId", required = false)
    private String commitId;

    @GatewayModelProperty(description = "应用codes", required = false)
    private List<String> applicationCodes;

    @GatewayModelProperty(description = "版本编码", required = false)
    private List<String> versionCodes;

    @GatewayModelProperty(description = "是否部署过生产", required = false)
    private List<Boolean> onlineStatus;

    @GatewayModelProperty(description = "创建人id", required = false)
    private List<Long> creatorIds;

    @GatewayModelProperty(description = "创建开始时间", required = false)
    private String createStart;

    @GatewayModelProperty(description = "创建结束时间查询", required = false)
    private String createEnd;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品code", required = true)
    private String productCode;
}
