package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * mirror info req
 *
 * <AUTHOR>
 * @date 2023-04-06 18:18
 */
@Data
@ZsmpModel(description = "获取镜像地址信息入参")
public class MirrorInfoReq  extends PageQueryBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "appId", required = true)
    private String appId;

    @ZsmpModelProperty(description = "是否查询生产镜像", sample = "true")
    private Boolean isPro;
}