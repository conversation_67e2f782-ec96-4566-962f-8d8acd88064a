package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.client.simple.User;
import com.zto.devops.framework.client.simple.Version;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.pipeline.client.model.release.enums.ReleaseEventType;
import com.zto.devops.pipeline.client.model.release.vo.ApolloChangeEventVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/13
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ApolloConflictVO extends EventConflictVO {

    private String appId;

    private String cluster;

    private String namespace;

    private List<ApolloConflictItem> events;

    public ApolloConflictVO(String appId, String cluster, String namespace) {
        this.appId = appId;
        this.cluster = cluster;
        this.namespace = namespace;
    }

    public void addConflict(ApolloConflictItem item) {
        if (CollectionUtil.isEmpty(events)) {
            events = new ArrayList<>();
        }
        events.add(item);
    }

    public List<ApolloConflictItem> getEvents() {
        if (CollectionUtil.isEmpty(events)) {
            return Collections.emptyList();
        }
        events.sort(Comparator.naturalOrder());
        return events;
    }

    @Data
    public static class ApolloConflictItem implements Comparable<ApolloConflictItem> {
        private String key;
        private String value;
        private String eventCode;
        private String versionCode;
        private String versionNum;
        private User operator;

        @Override
        public int compareTo(@NonNull ApolloConflictItem target) {
            if (StringUtil.isEmpty(key)) {
                return -1;
            }
            if (StringUtil.isEmpty(target.getKey())) {
                return 1;
            }
            return key.compareTo(target.getKey());
        }
    }
}
