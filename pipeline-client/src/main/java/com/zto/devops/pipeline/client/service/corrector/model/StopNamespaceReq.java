package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * stop namespace req
 *
 * <AUTHOR>
 * @date 2024-05-13 20:06
 */
@Data
public class StopNamespaceReq implements Serializable {
    @ZsmpModelProperty(description = "空间code", sample = "", required = true)
    private String namespaceCode;

    @ZsmpModelProperty(description = "动态多环境标签", sample = "", required = true)
    private Boolean autoEnvFlag;
}
