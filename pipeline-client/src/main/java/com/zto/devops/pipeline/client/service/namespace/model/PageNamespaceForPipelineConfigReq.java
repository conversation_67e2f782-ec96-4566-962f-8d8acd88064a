package com.zto.devops.pipeline.client.service.namespace.model;

import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.devops.pipeline.client.service.namespace.PageNamespaceReq;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * namespace page req
 *
 * <AUTHOR>
 * @date 2023-03-13 13:37
 */
@Getter
@Setter
public class PageNamespaceForPipelineConfigReq extends PageNamespaceReq implements Serializable {
    private static final long serialVersionUID = 1L;
    @GatewayModelProperty(description = "当前流水线编号", required = false)
    private String currentPipelineConfigCode;
    @GatewayModelProperty(description = "部署阶段环境:DEV(开发环境),FAT(测试环境)", required = false)
    private EnvEnum deployStageEnv;
}
