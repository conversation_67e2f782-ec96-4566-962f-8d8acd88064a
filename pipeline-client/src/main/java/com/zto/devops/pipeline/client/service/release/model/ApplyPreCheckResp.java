package com.zto.devops.pipeline.client.service.release.model;

import cn.hutool.core.date.DateUtil;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.pipeline.client.enums.FlowBusinessTypeEnum;
import com.zto.devops.pipeline.client.model.flow.entity.ReleaseWindowVO;
import com.zto.devops.pipeline.client.model.release.vo.apply.ApplyPreCheckGroup;
import com.zto.devops.pipeline.client.model.release.vo.apply.ApplyPreCheckResult;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ApplyPreCheckResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 能否操作
     */
    private boolean canOperate;

    /**
     * 检查列表
     */
    private List<ApplyPreCheckGroup> list;

    public boolean isCanOperate() {
        return CollectionUtil.isNotEmpty(list) &&
            list.stream()
                .filter(item->CollectionUtil.isNotEmpty(item.getResults()))
                .flatMap(item->item.getResults().stream())
                .allMatch(ApplyPreCheckResult::isCanOperate);
    }
}
