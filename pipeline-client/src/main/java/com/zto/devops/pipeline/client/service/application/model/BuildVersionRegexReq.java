package com.zto.devops.pipeline.client.service.application.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author：<PERSON><PERSON><PERSON><PERSON>
 * @Description：
 * @Date：2025/7/16 14:22
 */
@Data
@ZsmpModel(description = "查询加固策略req")
public class BuildVersionRegexReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "是否版本号正则，默认false.true版本号(制品版本号)规则，false是构建版本号规则", sample = "true", required = true)
    private Boolean versionRegexFlag;
}
