package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.devops.pipeline.client.enums.ClusterStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/10/11
 * @Version 1.0
 */
@Data
public class ResetClusterStatusReq implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "集群编码")
    @NotNull(message = "集群编码不能为空")
    private String code;

    @GatewayModelProperty(description = "集群状态")
    @NotNull(message = "集群状态不能为空")
    private ClusterStatusEnum status;
}
