package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.pipeline.client.model.release.enums.ReleaseEventType;
import com.zto.devops.pipeline.client.service.req.DeployStrategyReq;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@GatewayModel(description = "查询上线计划部署实例")
@Setter
@Getter
public class ListDeployInstanceReq  implements Serializable {

    @GatewayModelProperty(description = "版本code")
    private String versionCode;

    @GatewayModelProperty(description = "事件类型")
    private ReleaseEventType eventType;

    @GatewayModelProperty(description = "筛选关键词",required = false)
    private String key;

    @GatewayModelProperty(description = "是否查询全量(false-查询未拆批次,true-查询全量数据)")
    private Boolean total;

    @GatewayModelProperty(description = "当前批次",required = false)
    private Integer deployBatch;
}
