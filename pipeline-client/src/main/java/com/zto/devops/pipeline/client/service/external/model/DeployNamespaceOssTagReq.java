package com.zto.devops.pipeline.client.service.external.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/9/3
 */
@Data
@ZsmpModel(description = "查询测试环境部署空间的ossTag入参")
public class DeployNamespaceOssTagReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 应用ID
     */
    @ZsmpModelProperty(description = "应用ID", required = true)
    private String appId;
}
