package com.zto.devops.pipeline.client.service.application.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ArtifactCodeReq implements Serializable {
    private static final long serialVersionUID = 1L;
    @GatewayModelProperty(description = "制品编码", sample = "制品编码")
    private String code;
}
