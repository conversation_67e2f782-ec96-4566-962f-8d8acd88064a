package com.zto.devops.pipeline.client.service.namespace.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.devops.pipeline.client.enums.NamespaceDeployPermissionsEnum;
import com.zto.devops.pipeline.client.enums.NamespaceDeployScopeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * add namespace req
 *
 * <AUTHOR>
 * @date 2023-03-14 10:25
 */
@Getter
@Setter
public class AddNamespaceReq implements Serializable {
    private static final long serialVersionUID = 1L;
    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;
    @GatewayModelProperty(description = "环境", required = false)
    private EnvEnum env;
    @GatewayModelProperty( description = "空间名称", sample = "空间名称")
    private String name;
    @GatewayModelProperty( description = "空间描述", sample = "空间描述", required = false)
    private String description;
    @GatewayModelProperty(description = "空间部署范围", sample = "DEVELOP_PHASE", required = false)
    private NamespaceDeployScopeEnum deployScope;
    @GatewayModelProperty(description = "空间部署权限", sample = "DEVELOPER", required = false)
    private NamespaceDeployPermissionsEnum deployPermissions;
    @GatewayModelProperty( description = "空间oss标签", sample = "dev1")
    private String ossTag;
    @GatewayModelProperty( description = "空间部署分支类型。1开发分支，2发布分支.3开发分支+发布分支", sample = "1", required = false)
    private Integer deployBranchType;
}
