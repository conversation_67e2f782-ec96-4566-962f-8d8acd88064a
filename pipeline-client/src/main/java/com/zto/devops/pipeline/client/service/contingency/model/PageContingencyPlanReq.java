package com.zto.devops.pipeline.client.service.contingency.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.enums.contingency.ContingencyPlanLevelEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ZsmpModel(description = "预案列表分页查询req")
public class PageContingencyPlanReq extends PageQueryBase implements Serializable {
    @ZsmpModelProperty(description = "所属产品Code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "业务影响", required = false)
    private ContingencyPlanLevelEnum contingencyPlanLevel;

    @ZsmpModelProperty(description = "名称或描述", required = false)
    private String nameOrDesc;

}
