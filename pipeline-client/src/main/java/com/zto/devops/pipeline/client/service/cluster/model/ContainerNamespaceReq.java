package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/8/30
 * @Version 1.0
 */
@Data
@GatewayModel(description = "容器空间接口")
public class ContainerNamespaceReq implements Serializable {

    private static final long serialVersionUID = 1L;


    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty( description = "产品编码")
    @NotBlank(message = "产品编码不能为空")
    private String productCode;
    @GatewayModelProperty(description = "容器集群名称")
    @NotBlank(message = "容器集群名称不能为空")
    private String containerClusterName;
}
