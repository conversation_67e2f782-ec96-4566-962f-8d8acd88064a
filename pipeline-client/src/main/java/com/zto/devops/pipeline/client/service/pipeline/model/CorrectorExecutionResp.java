package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.devops.pipeline.client.enums.PlanStatusEnum;
import com.zto.devops.pipeline.client.enums.TaskTargetEnum;
import com.zto.devops.pipeline.client.enums.TaskTypeEnum;
import com.zto.devops.pipeline.client.enums.pipeline.PipelineExecutionTriggerSourceEnum;
import com.zto.devops.pipeline.client.enums.pipeline.PipelineExecutionTriggerTypeEnum;
import com.zto.devops.pipeline.client.enums.pipeline.PipelinePhaseEnum;
import com.zto.devops.pipeline.client.model.application.entity.ApplicationSimpleVO;
import com.zto.devops.pipeline.client.model.cluster.entity.NamespaceSimpleVO;
import com.zto.devops.pipeline.client.model.pipeline.entity.PipelineExecutionNodeSimpleVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: cher
 * @Description:
 */
@Data
@ZsmpModel(description = "流水线实例详情VO")
public class CorrectorExecutionResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "流水线编码", sample = "1")
    private String executionCode;

    /**
     * 流水线配置编码
     */
    @ZsmpModelProperty(description = "", sample = "1")
    private String executionConfigCode;

    /**
     * 流水线配置名称
     */
    @ZsmpModelProperty(description = "", sample = "1")
    private String executionConfigName;

    @ZsmpModelProperty(description = "一站式产品编码", sample = "1")
    private String productCode;

    @ZsmpModelProperty(description = "一站式产品名称", sample = "1")
    private String productName;

    /**
     * 流水线上下文 json
     */
    private Map<String, Object> pipelineContext;

    /**
     * 流水线阶段 枚举 开发、测试、回归、发布
     */
    private PipelinePhaseEnum pipelinePhase;
    @ZsmpModelProperty(description = "流水线阶段", sample = "回归阶段")
    private String pipelinePhaseName;

    @ZsmpModelProperty(description = "流水线阶段", sample = "[测试阶段、回归阶段]")
    private List<String> pipelinePhaseNameList;

    /**
     * 触发来源 枚举 部署、重启、回滚、停止、扩容、缩容、全网切换、切换应用类型
     */
    private PipelineExecutionTriggerSourceEnum triggerSource;

    @ZsmpModelProperty(description = "流水线序号", sample = "1")
    private Long pipelineSerialNumber;

    @ZsmpModelProperty(description = "触发方式 枚举 手动、自动", sample = "1")
    private PipelineExecutionTriggerTypeEnum triggerType;
    @ZsmpModelProperty(description = "触发方式名称 枚举 手动、自动", sample = "1")
    private String triggerTypeName;

    @ZsmpModelProperty(description = "目标类型 枚举 流程、空间、应用、集群、实例", sample = "1")
    private TaskTargetEnum targetType;

    @ZsmpModelProperty(description = "目标code", sample = "1")
    private String targetCode;

    @ZsmpModelProperty(description = "目标名称", sample = "1")
    private String targetName;

    @ZsmpModelProperty(description = "执行开始时间(运行时间)", sample = "1")
    private Date gmtStarted;
    @ZsmpModelProperty(description = "执行结束时间", sample = "1")
    private Date gmtEnded;

    @ZsmpModelProperty(description = "状态: 等待中/进行中/成功/失败/中断", sample = "1")
    private PlanStatusEnum status;
    @ZsmpModelProperty(description = "状态: 未开始/进行中/成功/失败/终止")
    private String statusDesc;

    @ZsmpModelProperty(description = "创建人id(运行人)")
    private Long creatorId;
    @ZsmpModelProperty(description = "创建人姓名(运行人)")
    private String creator;

    @ZsmpModelProperty(description = "运行详情集合", required = false)
    private List<PipelineExecutionNodeSimpleVO> executionNodeList;
    @ZsmpModelProperty(description = "运行空间", required = false)
    private List<NamespaceSimpleVO> namespaceList;
    @ZsmpModelProperty(description = "应用名称", required = false)
    private List<ApplicationSimpleVO> applicationList;

    @ZsmpModelProperty(description = "持续时间(单位秒)", sample = "1000", required = false)
    private Long continueTime;

    @ZsmpModelProperty(description = "版本名", sample = "版本名字", required = false)
    private String verName;

    @ZsmpModelProperty(description = "版本号", sample = "版本号", required = false)
    private String versionNum;

    @ZsmpModelProperty(description = "任务类型", sample = "任务类型", required = false)
    private TaskTypeEnum taskType;
}
