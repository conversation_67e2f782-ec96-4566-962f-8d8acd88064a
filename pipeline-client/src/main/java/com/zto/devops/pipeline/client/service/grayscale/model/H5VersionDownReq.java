package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * h5 version down req
 *
 * <AUTHOR>
 * @date 2025-06-09 13:40
 */
@Data
@ZsmpModel(description = "H5版本下线req")
public class H5VersionDownReq implements Serializable {
    @Auth(type = AuthTypeConstant.PRODUCT)
    @ZsmpModelProperty(description = "产品编码", sample = "product", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "id", sample = "1", required = true)
    private Long id;
}
