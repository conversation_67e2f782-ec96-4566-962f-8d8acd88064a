package com.zto.devops.pipeline.client.service.external.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: cher
 * @Date: 2023/12/26 18:27
 **/
@Data
@ZsmpModel(description = "动态多环境sdk配置出参")
public class AutoEnvSdkConfigResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "识别动态多环境域名的正则", required = true)
    private String recognizeAutoEnvRegular;

    @ZsmpModelProperty(description = "替换动态多环境域名的正则", required = true)
    private String replaceAutoEnvRegular;

    @ZsmpModelProperty(description = "请求头需要添加的key", required = true)
    private List<String> headerKeys;
}
