package com.zto.devops.pipeline.client.service.checkItem.model;

import com.zto.devops.pipeline.client.enums.flow.CheckActionEnum;
import com.zto.devops.pipeline.client.enums.flow.CheckItemTypeEnum;
import com.zto.devops.pipeline.client.enums.flow.CheckTypeEnum;
import com.zto.devops.pipeline.client.model.checkItem.CheckScope;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/30
 */
@ZsmpModel(description = "检查项列表入参")
@Data
public class QueryCheckItemReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "检查项规则", sample = "[BRANCH_CHECK]")
    private List<CheckTypeEnum> checkRules;

    @ZsmpModelProperty(description = "检查类型", sample = "[CHECK]")
    private List<CheckItemTypeEnum> itemTypes;

    @ZsmpModelProperty(description = "触发动作", sample = "[SUBMIT_TEST]")
    private List<CheckActionEnum> checkActions;

    @ZsmpModelProperty(description = "状态", sample = "true")
    private Boolean status;

    @ZsmpModelProperty(description = "正向作用域")
    private CheckScope forwardScope;

    @ZsmpModelProperty(description = "反向作用域")
    private CheckScope reverseScope;
}
