package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.devops.pipeline.client.enums.releaseWindow.ReleaseWindowTargetTypeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 添加或更新特批产品入参
 * @Author: cher
 * @Date: 2022/9/24 14:32
 **/
@Data
@ZsmpModel(description = "添加或更新特批产品入参")
public class AddSpecialProductReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "对象列表", sample = "[]", required = false)
    private List<String> targetCodes;

    @ZsmpModelProperty(description = "对象类型")
    private ReleaseWindowTargetTypeEnum targetType;

    @ZsmpModelProperty(description = "特批发布开始时间", sample = "2020-11-11 00:00:00", required = true)
    private Date startTime;

    @ZsmpModelProperty(description = "特批发布结束时间", sample = "2020-11-11 23:59:59", required = true)
    private Date endTime;
}
