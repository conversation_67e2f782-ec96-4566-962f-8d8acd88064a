package com.zto.devops.pipeline.client.service.namespace.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * namespace code req
 *
 * <AUTHOR>
 * @date 2023-03-17 16:17
 */
@Getter
@Setter
public class NamespaceCodeReq implements Serializable {
    private static final long serialVersionUID = 1L;
    @Auth(type = AuthTypeConstant.NAMESPACE)
    @GatewayModelProperty( description = "空间编码", sample = "空间编码")
    private String code;
}
