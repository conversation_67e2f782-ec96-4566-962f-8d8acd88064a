package com.zto.devops.pipeline.client.service.namespace.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * find rollback preview req
 *
 * <AUTHOR>
 * @date 2023-03-17 15:28
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FindRollbackPreviewReq implements Serializable {
    private static final long serialVersionUID = 1L;
    @Auth(type = AuthTypeConstant.NAMESPACE)
    @GatewayModelProperty( description = "空间编码", sample = "NS202101010001")
    private String namespaceCode;
    @GatewayModelProperty( description = "快照编码", sample = "NS202101010001")
    private String snapshotCode;
}
