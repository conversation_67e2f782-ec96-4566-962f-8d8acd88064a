package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/8/31
 * @Version 1.0
 */
@Data
@GatewayModel(description = "实例日志跳转地址")
public class LogUrlResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 容器-容器日志地址
     */
    private String containerLogUrl;

    /**
     * 容器-容器地址(同containerLogUrl参数不一致)
     */
    private String deploymentLogUrl;

    /**
     * 容器-zke地址
     */
    private String detailUrl;

    /**
     * 容器-终端地址
     */
    private String terminalUrl;

    /**
     * 虚拟机-堡垒机地址
     */
    private String jumpUrl;

    /**
     * 虚拟机-堡垒机（只读）地址
     */
    private String readOnlyJumpUrl;
}
