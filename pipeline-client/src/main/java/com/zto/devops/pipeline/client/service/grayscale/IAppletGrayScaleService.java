package com.zto.devops.pipeline.client.service.grayscale;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.grayscale.entity.GrayscaleVO;
import com.zto.devops.pipeline.client.service.grayscale.model.*;

/**
 * <AUTHOR>
 */
public interface IAppletGrayScaleService {

    /**
     * 检查小程序版本操作
     *
     * @param req 检查小程序版本操作入参
     * @return 校验结果
     */
    Result<CheckAppletVersionOperateResp> checkAppletVersionOperate(CheckAppletVersionOperateReq req);

    /**
     * 小程序版本上下线
     *
     * @param req 小程序版本上下线入参
     * @return
     */
    Result<Void> upAndDownAppletVersion(AppletVersionUpAndDownReq req);

    /**
     * 编辑发布小程序
     *
     * @param req 编辑发布小程序入参
     * @return
     */
    Result<Void> editPublishAppletVersion(AppletGrayscaleReq req);

    /**
     * 小程序灰度列表查询
     *
     * @param req req
     * @return applet gray scale list
     */
    PageResult<GrayscaleVO> pageAppletList(PageGrayscaleReq req);

    /**
     * 小程序灰度页面详情
     * @param req
     * @return void
     */
    Result<GrayscaleVO> appletDetail(AppletVersionEditDetailReq req);
}
