package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.devops.pipeline.client.model.cluster.entity.ApplicationNamespaceRollbackLineVO;
import com.zto.devops.pipeline.client.model.cluster.entity.NamespaceRollbackLineVO;
import com.zto.devops.pipeline.client.model.grayscale.entity.GrayscaleSimpleVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NamespaceRollbackPreviewResp implements Serializable {
    private static final long serialVersionUID = 1L;

//    @GatewayModelProperty( description = "应用回滚明细", sample = "[]")
//    private List<ApplicationNamespaceRollbackLineVO> applicationList;

    @GatewayModelProperty( description = "回滚明细", sample = "[]")
    private List<NamespaceRollbackLineVO> rollbackLines;

    @GatewayModelProperty( description = "灰度列表", sample = "[]")
    private List<GrayscaleSimpleVO> grayscales;

    public NamespaceRollbackPreviewResp(List<NamespaceRollbackLineVO> rollbackLines) {
        this.rollbackLines = rollbackLines;
    }
}
