package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.pipeline.PipelineNodeTypeEnum;
import com.zto.devops.pipeline.client.enums.pipeline.PipelinePhaseEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@ZsmpModel(description = "重置流水线配置节点开关")
@Data
public class ResetPipelineConfigNodeSwitchReq implements Serializable {

    //@Auth(type = AuthTypeConstant.PRODUCT)
    @ZsmpModelProperty(description = "产品编码", required = true )
    private List<String> productCodeList;

    @ZsmpModelProperty(description = "配置编号", required = false)
    private List<String> configCodeList;

    @ZsmpModelProperty(description = "阶段", required = true )
    private PipelinePhaseEnum pipelinePhase;

    @ZsmpModelProperty(description = "类型", required = true )
    private PipelineNodeTypeEnum configNodeTyp;

    @ZsmpModelProperty(description = "名称", required = false )
    private String configNodeName;

    @ZsmpModelProperty(description = "开关", required = true )
    private Boolean nodeSwitch;


}
