package com.zto.devops.pipeline.client.service.configset;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.configset.entity.ConfigSetKeyVO;
import com.zto.devops.pipeline.client.model.configset.entity.ConfigSetValueVO;
import com.zto.devops.pipeline.client.service.configset.model.*;

import java.util.List;

public interface IConfigSetService {

    Result<Void> addKey(AddKeyReq req);

    Result<List<ConfigSetKeyVO>> listKey(ListKeyReq req);

    Result<Void> editKey(EditKeyReq req);

    Result<Void> deleteKey(DeleteKeyReq req);

    Result<List<ConfigSetValueVO>> listValue(ListValueReq req);

    Result<ConfigSetValueVO> detailValue(ValueCodeReq req);

    Result<Void> deleteValue(ValueCodeReq req);

    Result<Void> onlineValue(ValueCodeReq req);

    Result<Void> offlineValue(ValueCodeReq req);

    Result<Void> editValue(EditValueReq req);

    Result<Void> copyValue(CopyValueReq req);
}
