package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.pipeline.client.model.instance.entity.InstanceVO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collection;

/**
 * @author: renx<PERSON><PERSON>
 * @date: Created in 2024/8/2 10:24
 */
@Setter
@Getter
public class InstancePodPageResult<T> extends PageResult<T> implements Serializable {

    private InstanceVO instance;

    public InstancePodPageResult(Collection list, int page, int size, long total, InstanceVO instance) {
        super(list, page, size, total);
        this.instance = instance;
    }

}
