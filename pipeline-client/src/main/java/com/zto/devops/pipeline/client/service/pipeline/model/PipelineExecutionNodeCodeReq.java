package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenguangyu
 * @Description:
 * @Date: 2023/6/28 14:53
 */
@ZsmpModel(description = "流水线-运行-节点详情入参实体")
@Data
public class PipelineExecutionNodeCodeReq implements Serializable {

    @Auth(type = AuthTypeConstant.PIPELINE_EXECUTION_NODE)
    @ZsmpModelProperty(description = "流水线节点编码", sample = "1", required = true)
    private String executionNodeCode;
}
