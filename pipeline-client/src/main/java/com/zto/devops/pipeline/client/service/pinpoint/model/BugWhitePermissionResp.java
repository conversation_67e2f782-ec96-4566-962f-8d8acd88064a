package com.zto.devops.pipeline.client.service.pinpoint.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ZsmpModel(description = "缺陷白名单操作权限响应")
@Data
public class BugWhitePermissionResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "是否已经操作忽略")
    private Boolean operated;
    @ZsmpModelProperty(description = "已经操作忽略原因")
    private String operatedMsg;
    @ZsmpModelProperty(description = "是否有操作权限")
    private Boolean operatePermission;
}
