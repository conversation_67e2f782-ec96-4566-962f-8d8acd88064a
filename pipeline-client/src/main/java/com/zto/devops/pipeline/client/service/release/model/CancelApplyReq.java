package com.zto.devops.pipeline.client.service.release.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/11
 * @Version 1.0
 */
@Data
public class CancelApplyReq implements Serializable {

    @GatewayModelProperty(description = "事件编码")
    private List<String> codes;

    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "版本编码")
    private String versionCode;
}
