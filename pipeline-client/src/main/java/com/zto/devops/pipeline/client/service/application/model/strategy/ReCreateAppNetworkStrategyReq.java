package com.zto.devops.pipeline.client.service.application.model.strategy;

import com.zto.devops.pipeline.client.model.application.entity.strategy.CreateNetworkEgressVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ZsmpModel(description = "重新创建应用出口网络策略Req")
public class ReCreateAppNetworkStrategyReq implements Serializable {
    private static final long serialVersionUID = -642195065226181481L;

    @ZsmpModelProperty(description = "应用网络策略请求入参", sample = "1", required = true)
    private List<CreateNetworkEgressVO> vos;
}
