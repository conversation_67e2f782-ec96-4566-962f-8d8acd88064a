package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.devops.pipeline.client.enums.releaseWindow.ReleaseWindowTargetTypeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ZsmpModel(description = "更新特批产品入参")
public class UpdateSpecialProductReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "对象id")
    private String id;

    @ZsmpModelProperty(description = "特批发布开始时间", sample = "2020-11-11 00:00:00", required = true)
    private Date startTime;

    @ZsmpModelProperty(description = "特批发布结束时间", sample = "2020-11-11 23:59:59", required = true)
    private Date endTime;
}
