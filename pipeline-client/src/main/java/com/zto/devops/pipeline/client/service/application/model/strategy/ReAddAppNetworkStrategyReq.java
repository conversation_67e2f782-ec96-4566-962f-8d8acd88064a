package com.zto.devops.pipeline.client.service.application.model.strategy;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @Date: 2024/7/11 11:26
 */
@Data
@ZsmpModel(description = "重新申请应用网络策略Req")
public class ReAddAppNetworkStrategyReq extends AddAppNetworkStrategyReq implements Serializable {

    @ZsmpModelProperty(description = "应用网络策略编码", sample = "1", required = true)
    private String appNetworkStrategyCode;
}
