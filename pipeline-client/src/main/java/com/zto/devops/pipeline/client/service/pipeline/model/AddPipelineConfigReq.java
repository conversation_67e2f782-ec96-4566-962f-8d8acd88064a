package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.enums.PipelineConfigTypeEnum;
import com.zto.devops.pipeline.client.model.pipeline.entity.PipelineConfigNodeLinkVO;
import com.zto.devops.pipeline.client.model.pipeline.entity.PipelineConfigNodeVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


@ZsmpModel(description = "流水线配置")
@Data
public class AddPipelineConfigReq implements Serializable {

    @ZsmpModelProperty(description = "配置名称", required = true)
    private String pipelineConfigName;

    @ZsmpModelProperty(description = "配置描述")
    private String description;

    @ZsmpModelProperty(description = "配置类型", required = true)
    private PipelineConfigTypeEnum type;

    @ZsmpModelProperty(description = "产品编码")
    private String productCode;

    @ZsmpModelProperty(description = "流水线配置节点", required = true)
    private List<PipelineConfigNodeVO> nodes;

    @ZsmpModelProperty(description = "流水线配置节点连线", required = true)
    private List<PipelineConfigNodeLinkVO> links;

}
