package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.enums.ActionEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: cher
 * @Date: 2023/5/30 17:34
 **/
@Data
public class FixRuleToMqReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "灰度规则编码", required = true)
    private List<String> codes;

    @ZsmpModelProperty(description = "灰度规则同步类型", required = true)
    private ActionEnum actionEnum;
}
