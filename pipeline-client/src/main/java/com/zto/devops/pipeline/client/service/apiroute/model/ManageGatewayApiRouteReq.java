package com.zto.devops.pipeline.client.service.apiroute.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.OperateEnum;
import com.zto.devops.pipeline.client.model.apiroute.entity.GatewayApiRouteInfoVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * api网关/流量网关路由更新请求
 *
 * <AUTHOR>
 * @date 2024-10-23 15:52
 */
@Data
public class ManageGatewayApiRouteReq implements Serializable {
    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品code")
    private String productCode;

    @GatewayModelProperty(description = "操作类型")
    private OperateEnum operateType;

    @GatewayModelProperty(description = "要更新的路由")
    private List<GatewayApiRouteInfoVO> gatewayApiRouteVOList;

    @GatewayModelProperty(description = "空间code", required = false)
    private String namespaceCode;
}
