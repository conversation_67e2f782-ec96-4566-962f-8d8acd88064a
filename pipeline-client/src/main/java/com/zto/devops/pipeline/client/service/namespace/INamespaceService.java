package com.zto.devops.pipeline.client.service.namespace;

import com.zto.devops.framework.client.dto.PageObjectResult;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.pipeline.client.model.cluster.entity.FindVersionByNamespaceVO;
import com.zto.devops.pipeline.client.model.cluster.entity.NamespaceVO;
import com.zto.devops.pipeline.client.model.cluster.entity.PageNamespaceVO;
import com.zto.devops.pipeline.client.model.cluster.query.FindVersionByNamespaceQuery;
import com.zto.devops.pipeline.client.model.cluster.query.PageNamespaceAllQuery;
import com.zto.devops.pipeline.client.model.common.vo.EnumRelateVO;
import com.zto.devops.pipeline.client.service.cluster.model.*;
import com.zto.devops.pipeline.client.service.namespace.model.*;
import com.zto.devops.pipeline.client.service.namespace.model.AddNamespaceReq;
import com.zto.devops.pipeline.client.service.namespace.model.AppMigrationNamespaceReq;
import com.zto.devops.pipeline.client.service.namespace.model.ApplicationNamespaceResp;
import com.zto.devops.pipeline.client.service.namespace.model.EditNamespaceReq;
import com.zto.devops.pipeline.client.service.namespace.model.FindRollbackPreviewReq;
import com.zto.devops.pipeline.client.service.namespace.model.ListNamespaceReq;
import com.zto.devops.pipeline.client.service.namespace.model.NamespaceCodeReq;
import com.zto.devops.pipeline.client.service.namespace.model.NamespaceDeployFieldReq;
import com.zto.devops.pipeline.client.service.namespace.model.NamespaceDeployFieldResp;
import com.zto.devops.pipeline.client.service.namespace.model.NamespaceRollbackCheckResp;
import com.zto.devops.pipeline.client.service.namespace.model.RollbackPreviewResp;
import com.zto.devops.pipeline.client.service.req.ProductCodeOnlyReq;
import com.zto.devops.pipeline.client.service.req.refresh.RefreshBaseNamespaceOssTagReq;

import java.util.List;
import java.util.Map;

/**
 * namespace service
 *
 * <AUTHOR>
 * @date 2023-03-13 13:28
 **/
public interface INamespaceService {
    Result<Boolean> checkFatNamespace(String productCode);

    Result<Void> addNamespace(AddNamespaceReq req);

    /**
     * 分页查空间列表(流水线配置)
     *
     * @param req
     * @return result
     */
    PageResult<NamespaceVO> pageNamespaceForPipelineConfig(PageNamespaceForPipelineConfigReq req);

    /**
     * 分页查空间列表
     *
     * @param req
     * @return result
     */
    PageResult<NamespaceVO> pageNamespace(com.zto.devops.pipeline.client.service.namespace.PageNamespaceReq req);

    /**
     * 查空间列表
     *
     * @param req req
     * @return list
     */
    Result<List<NamespaceVO>> listNamespace(ListNamespaceReq req);

    /**
     * 查询空间编辑的特殊字段
     *
     * @param req req
     * @return resp
     */
    Result<NamespaceDeployFieldResp> queryNamespaceDeployField(NamespaceDeployFieldReq req);

    /**
     * 应用迁移查询空间
     *
     * @param req req
     * @return list
     */
    Result<List<ApplicationNamespaceResp>> appMigrationNamespace(AppMigrationNamespaceReq req);

    Result<RollbackPreviewResp> findRollbackPreview(FindRollbackPreviewReq req);

    Result<NamespaceRollbackCheckResp> rollbackNamespacePreCheck(NamespaceCodeReq req);

    Result<NamespaceRollbackPreviewResp> findNamespaceRollbackPreview(FindNamespaceRollbackPreviewReq req);

    Result<List<NamespaceVO>> listUpNamespace(NamespaceCodeReq req);

    PageObjectResult<PageNamespaceVO> pageNamespaceAll(PageNamespaceAllReq req);

    PageObjectResult<PageNamespaceVO> pageNamespaceAllExtend(PageNamespaceExtendReq req);

    Result<String> checkEditNamespace(EditNamespaceReq req);
    /**
     * 编辑空间
     *
     * @param req req
     * @return void
     */
    Result<Void> editNamespace(EditNamespaceReq req);

    /**
     * 编辑空间标签
     *
     * @param req req
     * @return void
     */
    Result<Void> editNamespaceTag(EditNamespaceTagReq req);

    /**
     * 移除空间
     *
     * @param req req
     * @return void
     */
    Result<Void> removeNamespace(NamespaceCodeReq req);

    /**
     * 迁移空间
     *
     * @param req req
     * @return void
     */
    Result<Void> migrationNamespace(MigrationNamespaceReq req);

    /**
     * 刷新空间oss标签
     *
     * @return void
     */
    Result<Void> refreshOssTag();

    /**
     * 回滚空间
     *
     * @param req req
     * @return void
     */
    Result<Void> rollbackNamespace(RollbackNamespaceReq req);

    /**
     * 回滚指定空间制品包
     *
     * @param req req
     * @return void
     */
    Result<Void> rollbackSpecifiedNamespace(RollbackSpecifiedNamespaceReq req);

    /**
     * 查询版本空间列表
     * */
    Result<List<NamespaceVO>> versionNamespaceList(ProductCodeOnlyReq req);

    /**
     *刷新base空间OssTag(BASE,BASE_DEV,BASE_FAT)
     * */
    Result<Map> refreshBaseNamespaceOssTag(RefreshBaseNamespaceOssTagReq req);

    /**
     * 刷新空间的部署分支类型
     * */
    Result<Void> refreshNamespaceDeployBranchType(String productCode);

    /**
     * 查询空间下的应用列表
     *
     * @param req
     * @return list
     */
    Result<List<AppByNamespaceResp>> listAppByNamespace(AppByNamespaceReq req);

    /**
     * 为所有没有灾备空间的产品添加灾备空间
     *
     * @return
     */
    Result<Void> refreshBackupNamespace();


    /**
     * 根据版本 查询空间标签  并排序
     *
     * @param productCode   versionCode
     * @return result
     */
    List<NamespaceVO> getNamespaceTagByVersionCode(String productCode, String versionCode, User user);

    PageNamespaceVO pageNamespace(PageNamespaceAllQuery query);

    PageNamespaceVO pageAllNamespace(PageNamespaceAllQuery query);

    /**
     * 根据appId和空间名称，查询版本（qc使用）
     *
     * @param query {@link FindVersionByNamespaceQuery}
     * @return {@link FindVersionByNamespaceVO}
     */
    FindVersionByNamespaceVO queryVersionByNamespace(FindVersionByNamespaceQuery query);
    /**
     *刷新集成空间
     * */
    Result<Void> refreshBaseTestNamespace(List<String> productCodes);
    /**
     *删除集成空间
     * */
    Result<Void> deleteBaseTestNamespace(List<String> productCodes);

    /**
     * 返回所有的空间类型枚举
     * @return list
     */
    Result<List<EnumRelateVO>> queryAllNamespaceType();
}