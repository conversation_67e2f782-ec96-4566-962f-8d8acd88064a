package com.zto.devops.pipeline.client.service.release.model;

import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


@GatewayModel(description = "上线计划发布配置比较")
@Setter
@Getter
public class FindPlanConfigDiffReq implements Serializable {

    @GatewayModelProperty(description = "产品code")
    private String productCode;

    @GatewayModelProperty(description = "产品code")
    private String versionCode;

    @GatewayModelProperty(description = "事件code")
    private String eventCode;


}
