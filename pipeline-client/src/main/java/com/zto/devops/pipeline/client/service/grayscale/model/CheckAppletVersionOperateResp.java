package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ZsmpModel(description = "小程序灰度操作检查resp")
public class CheckAppletVersionOperateResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "检查结果", sample = "true", required = true)
    private Boolean result;

    @ZsmpModelProperty(description = "提示信息")
    private String message;

    @ZsmpModelProperty(description = "操作按钮列表", required = true)
    private List<String> operations;
}
