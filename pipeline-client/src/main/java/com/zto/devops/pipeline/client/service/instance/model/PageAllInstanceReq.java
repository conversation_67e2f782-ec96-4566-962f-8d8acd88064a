package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class PageAllInstanceReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 1L;
    @GatewayModelProperty(description = "实例编码", required = false)
    private String code;
    @GatewayModelProperty(description = "enable", required = false)
    private Boolean enable;
    @GatewayModelProperty(description = "动态多环境标签", required = false)
    private Boolean autoEnvFlag;
}
