package com.zto.devops.pipeline.client.service.external.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: cher
 * @Date: 2023/12/26 17:32
 **/
@Data
@ZsmpModel(description = "查询动态多环境版本出参")
public class ListAutoEnvVersionResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "版本号", required = true)
    private String versionNum;

    @ZsmpModelProperty(description = "版本名称", required = true)
    private String versionName;

    @ZsmpModelProperty(description = "动态版本空间", required = true)
    private List<ListAutoEnvNamespaceVO> namespaces;

}
