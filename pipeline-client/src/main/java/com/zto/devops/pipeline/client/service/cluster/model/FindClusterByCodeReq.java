package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/8/16
 * @Version 1.0
 */
@Data
public class FindClusterByCodeReq implements Serializable {

    @Auth(type = AuthTypeConstant.CLUSTER)
    private String code;
}
