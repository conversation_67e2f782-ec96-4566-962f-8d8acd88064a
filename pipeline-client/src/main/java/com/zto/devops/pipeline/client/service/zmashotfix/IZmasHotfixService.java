package com.zto.devops.pipeline.client.service.zmashotfix;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.zmas.ZmasHotfixDetailVO;
import com.zto.devops.pipeline.client.service.req.CreateZmasHotfixReq;
import com.zto.devops.pipeline.client.service.req.DeleteZmasHotfixReq;
import com.zto.devops.pipeline.client.service.req.QueryZmasHotfixReq;

import java.util.List;

/**
 * zmas热更新
 */
public interface IZmasHotfixService {

    Result<Void> createHotfix(CreateZmasHotfixReq req);

    Result<Void> updateHotfix(CreateZmasHotfixReq req);

    Result<Void> deleteHotfix(DeleteZmasHotfixReq req);

    PageResult<ZmasHotfixDetailVO> listZmasHotfix(QueryZmasHotfixReq req);
}
