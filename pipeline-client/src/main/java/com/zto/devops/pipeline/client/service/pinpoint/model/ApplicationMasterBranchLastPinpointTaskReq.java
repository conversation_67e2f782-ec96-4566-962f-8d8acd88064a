package com.zto.devops.pipeline.client.service.pinpoint.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date 2024-07-24 11:13
 */
@Data
public class ApplicationMasterBranchLastPinpointTaskReq implements Serializable {
    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "applicationCode")
    private String applicationCode;

}
