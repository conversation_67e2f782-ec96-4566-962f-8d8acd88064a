package com.zto.devops.pipeline.client.service.apiroute;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.apiroute.entity.ApiRouteVO;
import com.zto.devops.pipeline.client.service.apiroute.model.ApiRouteResp;
import com.zto.devops.pipeline.client.service.apiroute.model.ListApiRouteReq;
import com.zto.devops.pipeline.client.service.apiroute.model.ManageApiRouteReq;

import java.util.List;


public interface IApiRouteService {

    com.zto.titans.common.entity.Result<List<ApiRouteResp>> listApiRoute(ListApiRouteReq req);

    Result<List<ApiRouteVO>> listApiRouteByProduct(ListApiRouteReq req);

    Result<Void> manageApiRoute(ManageApiRouteReq req);

}
