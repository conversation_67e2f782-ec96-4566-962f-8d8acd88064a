package com.zto.devops.pipeline.client.service.grayscale;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.grayscale.entity.GrayscaleResp;
import com.zto.devops.pipeline.client.model.grayscale.entity.GrayscaleVO;
import com.zto.devops.pipeline.client.model.grayscale.entity.PageGrayListVO;
import com.zto.devops.pipeline.client.service.grayscale.model.*;

import java.util.List;

/**
 * gray scale service
 *
 * <AUTHOR>
 * @date 2023-03-20 18:06
 **/
public interface IGrayScaleService {
    Result<GrayscaleVO> getGrayscale(GrayscaleCodeReq req);

    Result<Void> publishGrayscale(GrayscaleReq req);

    Result<Void> onlineGrayscale(GrayscaleCodeReq req);

    Result<Void> unOnlineGrayscale(GrayscaleCodeReq req);

    com.zto.titans.common.entity.Result<PageGrayListVO> queryAllGrayList(ListAllGrayReq req);

    /**
     * 灰度分页查询
     *
     * @param req req
     * @return result
     */
    PageResult<GrayscaleVO> pageGrayscale(PageGrayscaleReq req);

    /**
     * 应用回滚灰度下线列表查询
     *
     * @param req
     * @return
     */
    Result<List<GrayscaleResp>> grayscaleRollbackList(GrayscaleRollbackReq req);


}