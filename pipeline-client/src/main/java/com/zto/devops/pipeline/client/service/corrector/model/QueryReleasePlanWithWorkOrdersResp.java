package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.pipeline.client.model.release.enums.ReleasePlanStatusEnum;
import com.zto.devops.pipeline.client.model.release.vo.WorkOrderDetailVO;
import com.zto.devops.pipeline.client.model.release.vo.apply.ReleasePlanWorkOrder;
import com.zto.devops.pipeline.client.model.release.vo.apply.WorkOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryReleasePlanWithWorkOrdersResp implements Serializable {

    /**
     * 上线计划编码
     */
    private String releasePlanCode;

    /**
     * 上线计划状态
     */
    private ReleasePlanStatusEnum status;

    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 工单列表
     */
    private List<WorkOrderDetailVO> workOrders;

    /**
     * 上线计划状态描述
     */
    private String statusDesc;

    public String getStatusDesc() {
        return Objects.nonNull(this.status) ? this.status.getValue() : "";
    }

    public void buildWorkOrders(List<ReleasePlanWorkOrder> workOrders) {
        if (CollectionUtil.isEmpty(workOrders)) {
            return;
        }
        this.setWorkOrders(workOrders.stream().map(item -> (convert(item.getWorkOrder()))).collect(Collectors.toList()));
    }

    private WorkOrderDetailVO convert(WorkOrder workOrder) {
        WorkOrderDetailVO vo = new WorkOrderDetailVO();
        vo.setStatus(workOrder.getStatus());
        vo.setWorkOrderCode(workOrder.getWorkOrderCode());
        vo.setWorkOrderType(workOrder.getWorkOrderType());
        vo.setWorkOrderSubType(workOrder.getWorkOrderSubType());
        vo.setTitle(workOrder.getTitle());
        return vo;
    }
}
