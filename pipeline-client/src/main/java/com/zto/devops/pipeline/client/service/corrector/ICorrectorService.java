package com.zto.devops.pipeline.client.service.corrector;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.entity.PageBasicConfigInfoVO;
import com.zto.devops.framework.client.enums.AggregateType;
import com.zto.devops.framework.client.enums.BasicConfigEnum;
import com.zto.devops.pipeline.client.enums.PlanStatusEnum;
import com.zto.devops.pipeline.client.model.cluster.entity.NamespaceVO;
import com.zto.devops.pipeline.client.model.flow.entity.FlowVO;
import com.zto.devops.pipeline.client.model.flow.entity.PlanLineOneVO;
import com.zto.devops.pipeline.client.model.grayscale.entity.GrayscaleVO;
import com.zto.devops.pipeline.client.model.instance.entity.InstanceVO;
import com.zto.devops.pipeline.client.service.application.model.DeleteApplicationBatchReq;
import com.zto.devops.pipeline.client.service.cluster.model.*;
import com.zto.devops.pipeline.client.service.corrector.model.*;
import com.zto.devops.pipeline.client.service.grayscale.model.FixRuleToMqReq;
import com.zto.devops.pipeline.client.service.grayscale.model.PageAllGrayscaleReq;
import com.zto.devops.pipeline.client.service.instance.model.InstanceStatusResp;
import com.zto.devops.pipeline.client.service.instance.model.PageAllInstanceReq;
import com.zto.devops.pipeline.client.service.instance.model.ResetInstanceStatusReq;
import com.zto.devops.pipeline.client.service.namespace.model.SwitchNamespaceReq;
import com.zto.devops.pipeline.client.service.pipeline.model.CorrectorExecutionResp;
import com.zto.devops.pipeline.client.service.req.*;

import java.util.List;

/**
 * @Author: cher
 * @Date: 2023/3/28 13:28
 **/
public interface ICorrectorService {

    /**
     * redis生成编码序号添加
     * 解决redis重启，编码序号重来问题
     *
     * @param aggregateType
     * @param num
     * @return 最后一次编码
     */
    Result<String> getRedisCode(AggregateType aggregateType, int num);

    /**
     * 批量删除应用及相关信息
     *
     * @param req
     * @return
     */
    Result<Void> batchDeleteApplication(DeleteApplicationBatchReq req);

    /**
     * 新增流程灰度
     *
     * @param flowCode flow code
     * @return result
     */
    Result addGrayscale(String flowCode);

    /**
     * 添加或更新特批产品
     *
     * @param req req
     * @return void
     */
    Result<Void> addOrUpdateSpecialProduct(AddSpecialProductReq req);

    /**
     * 更新特批产品
     *
     * @param req req
     * @return void
     */
    Result<Void> updateSpecialProduct(UpdateSpecialProductReq req);

    /**
     * 修改灰度状态
     *
     * @param req req
     * @return result
     */
    Result changeGrayscaleStatus(GrayscaleStatusChangedReq req);

    /**
     * 修改灰度状态标签
     *
     * @param req req
     * @return result
     */
    Result changeGrayscaleStatusTag(GrayscaleStatusTagChangedReq req);

    /**
     * 集群所有状态
     *
     * @return list
     */
    Result<List<ClusterStatusResp>> getClusterStatus();

    /**
     * 删除特批产品
     *
     * @param req req
     * @return void
     */
    Result<Void> deleteSpecialProduct(DeleteSpecialProductReq req);

    /**
     * 实例部署
     *
     * @param req req
     * @return void
     */
    Result<Void> deployAppByInstance(DeployInstanceReq req);

    /**
     * 流程所有状态
     *
     * @return flow status
     */
    Result<List<FlowStatusResp>> getFlowStatus();

    /**
     * 实例所有状态
     *
     * @return instance status
     */
    Result<List<InstanceStatusResp>> getInstanceStatus();

    /**
     * 获取全局所有集群
     *
     * @param req req
     * @return cluster resp
     */
    PageResult<PageClusterResp> listAllCluster(PageAllClusterReq req);

    /**
     * @param req req
     * @return flow vo
     */
    PageResult<FlowVO> listAllFlows(PageAllFlowReq req);

    /**
     * 获取全局所有实例
     *
     * @param req req
     * @return instance vo
     */
    PageResult<InstanceVO> listAllInstance(PageAllInstanceReq req);

    /**
     * 获取全局所有集群
     *
     * @param req req
     * @return namespace vo
     */
    PageResult<NamespaceVO> listAllNamespace(PageAllNamespaceReq req);

    /**
     * namespace所有状态
     *
     * @return namespace status
     */
    Result<List<NamespaceStatusResp>> getNamespaceStatus();

    /**
     * 隐藏或打开开发&测试空间
     *
     * @param req
     * @return
     */
    Result<Void> switchNamespace(SwitchNamespaceReq req);

    /**
     * 删除开发&测试空间资源：实例、集群、空间
     *
     * @param req
     * @return
     */
    Result<Void> deleteNamespaceResource(NamespaceCodeReq req);

    /**
     * 批量删除实例
     */
    Result deleteNamespaceResourceBatch(String codes);

    /**
     * 所有灰度分页查询
     *
     * @param req req
     * @return gray scale vo
     */
    PageResult<GrayscaleVO> pageAllGrayscale(PageAllGrayscaleReq req);

    /**
     * 获取planLine信息
     *
     * @param req req
     * @return plan line
     */
    Result<PlanLineOneVO> findPlanLine(PlanLineOneReq req);

    /**
     * planLine所有状态
     *
     * @return plan line status resp
     */
    Result<List<PlanLineStatusResp>> getPlanLineStatus();

    /**
     * 重置集群状态
     *
     * @param req req
     * @return void
     */
    Result<Void> resetClusterStatus(ResetClusterStatusReq req);

    /**
     * 重置流程状态
     *
     * @param req req
     * @return void
     */
    Result<Void> resetFlowStatus(ResetFlowStatusReq req);

    /**
     * 重置实例状态
     *
     * @param req req
     * @return void
     */
    Result<Void> resetInstanceStatus(ResetInstanceStatusReq req);

    /**
     * 重置空间状态
     *
     * @param req req
     * @return void
     */
    Result<Void> resetNamespaceStatus(ResetNamespaceStatusReq req);

    /**
     * 重置planLine状态
     *
     * @param req req
     * @return void
     */
    Result<Void> resetPlanLineStatus(ResetPlanLineStatusReq req);

    /**
     * 模拟回调-只能用luban网关，公网的回调回不来
     *
     * @param req req
     * @return void
     */
    Result<Void> simulateCallBack(CallBackReq req);

    Result<Void> simulateCallBackBatch(CallBackBatchReq req);

    /**
     * 获取流水线执行信息
     *
     * @param executionCode
     * @return
     */
    Result<List<CorrectorExecutionResp>> listByExecutionCode(String executionCode);

    /**
     * 根据执行编码刷新流水线状态
     *
     * @param executionCode 流水线执行编码
     * @return
     */
    Result<Void> resetPipelineStatus(String executionCode);

    /**
     * 特批产品列表
     *
     * @param req req
     * @return list
     */
    Result<List<SpecialProductListResp>> specialProductList(SpecialProductListReq req);

    /**
     * 修复灰度规则同步
     *
     * @param req
     * @return
     */
    Result<Void> fixRuleToMq(FixRuleToMqReq req);

    /**
     * 部署应用master代码到测试环境
     *
     * @return PlanCode
     */
    Result<String> deployApplicationToFat(DeployApplicationToFatReq req);

    /**
     * 重启测试环境实例
     *
     * @return PlanCode
     */
    Result<List<String>> restartApplicationToFat(RestartApplicationToFatReq req);

    /**
     * 获取plan状态
     */
    Result<PlanStatusEnum> queryPlanStatus(QueryPlanStatusReq req);

    /**
     * 停止空间
     *
     * @param req req
     * @return
     */
    Result<Void> stopNamespace(StopNamespaceReq req);

    /**
     * 重启空间
     *
     * @param req req
     * @return
     */
    Result<Void> restartNamespace(RestartNamespaceReq req);

    /**
     * jenkins容器化构建对比
     *
     * @param req req
     * @return planCode
     */
    Result<String> jenkinsApplicationBuild(ApplicationBuildReq req);

    /**
     * 根据plan查询结果
     *
     * @param req req
     * @return resp
     */
    Result<QueryPlanResp> queryPlanAndArtifact(QueryPlanAndArtifactReq req);

    /**
     * 打开动态多环境后，提供接口手动创建版本空间、集群，实例。(第一泳道，第二泳道,第三泳道版本)
     * 关闭动态多环境后，提供接口手动修改版本类型(flow_flag=false)，手动删除flow的版本空间/集群/实例
     *
     * @param req req
     * @return void
     */
    Result<Void> autoEnvSwitchOpt(AutoEnvSwitchOptReq req);


    /**
     * 为了推进动态多环境，计划合并非动态多环境产品的测试环境
     *
     * @param req req
     * @return void
     */
    Result<Void> mergeNamespaceOfNonAutoEnv(MergeNamespaceOfNonAutoEnvReq req);

    /**
     * @param req req
     * @return return
     */
    Result<Void> showInstance(ShowInstanceReq req);

    /**
     * list
     * @return list
     */
    Result<List<BasicConfigEnum>> getBasicConfigTypeList();

    Result<Void> addBasicConfig(AddBasicConfigReq req);

    Result<PageBasicConfigInfoVO> pageAllBasicConfig(PageAllBasicConfigReq req);

    Result<Void> updateBasicConfig(UpdateBasicConfigReq req);

    Result<Void> deleteBasicConfig(DeleteBasicConfigReq req);


    /**
     * 根据版本code，查上线计划包含工单信息.
     *
     * @param req req {@link QueryReleasePlanWithWorkOrdersReq}
     * @return list {@link QueryReleasePlanWithWorkOrdersResp}
     */
    Result<QueryReleasePlanWithWorkOrdersResp> queryReleasePlanWithWorkOrders(QueryReleasePlanWithWorkOrdersReq req);

    /**
     * 更新上线计划状态.
     *
     * @param req {@link  EditReleasePlanStatusReq}
     */
    Result<Void> editReleasePlanStatus(EditReleasePlanStatusReq req);

    /**
     * 删除上线计划工单.
     *
     * @param req {@link DeleteReleasePlanWorkOrderReq}
     */
    Result<Void> deleteReleasePlanWorkOrders(DeleteReleasePlanWorkOrderReq req);

    /**
     * @param req {@link RefreshReleasePlanWorkOrderReq}
     */
    Result<Void> refreshReleasePlanWorkOrders(RefreshReleasePlanWorkOrderReq req);

    /**
     * 主机房宕机，主动停止未终结的普通容器扩容申请单
     * @return void
     */
    Result<Void> cancelZkeClusterExpandForEngineRoomFault(CancelZkeClusterExpandReq req);
}
