package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/8/25
 * @Version 1.0
 */
@Data
public class ListArtifactReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.CLUSTER)
    @GatewayModelProperty(description = "集群code")
    private String clusterCode;

    @GatewayModelProperty(description = "制品包commit", required = false)
    private String commit;

    @GatewayModelProperty(description = "是否线上回滚推荐", required = false)
    private boolean isOnlineRollbackRecommend;
}
