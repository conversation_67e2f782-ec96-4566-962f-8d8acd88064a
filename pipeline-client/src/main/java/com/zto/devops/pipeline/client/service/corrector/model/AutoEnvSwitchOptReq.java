package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 动态多环境开关req
 *
 * <AUTHOR>
 * @date 2023-10-25 13:43
 */
@Data
public class AutoEnvSwitchOptReq implements Serializable {
    /**
     * 产品code
     */
    @ZsmpModelProperty(description = "产品code", required = true)
    private String productCode;

    /**
     * 开/关
     */
    @ZsmpModelProperty(description = "开关", required = true)
    private Boolean autoEnvFlag;
}
