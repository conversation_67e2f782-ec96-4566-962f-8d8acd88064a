package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @Date: 2022/9/26 9:47
 */
@Data
@GatewayModel(description = "灰度状态变更req")
public class GrayscaleStatusChangedReq implements Serializable {

    @ZsmpModelProperty(description = "灰度编码code", sample = "1", required = true)
    private String code;
    @ZsmpModelProperty(description = "状态", sample = "1", required = true)
    private String status;
}
