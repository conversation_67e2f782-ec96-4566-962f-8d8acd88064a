package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.BuildTypeEnum;
import com.zto.devops.pipeline.client.enums.DeployTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2021/8/17
 */
@Data
public class ApplicationTemplateReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "构建类型",required = false)
    private BuildTypeEnum buildType;

    @GatewayModelProperty(description = "部署类型",required = false)
    private DeployTypeEnum deployType;
}
