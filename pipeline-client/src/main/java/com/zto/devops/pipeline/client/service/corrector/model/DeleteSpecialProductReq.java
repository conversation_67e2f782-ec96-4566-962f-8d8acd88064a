package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 删除特批产品入参
 * @Author: cher
 * @Date: 2022/9/24 14:32
 **/
@Data
@ZsmpModel(description = "删除特批产品入参")
public class DeleteSpecialProductReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "产品列表", sample = "[]")
    private List<String> targetCodes;

    @ZsmpModelProperty(description = "特批数据主键", sample = "")
    private String id;
}
