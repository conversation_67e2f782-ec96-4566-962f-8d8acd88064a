package com.zto.devops.pipeline.client.service.release;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.pipeline.client.model.apply.entity.ZbaseApplyViewVO;
import com.zto.devops.pipeline.client.model.flow.entity.FlowApplyReleaseVO;
import com.zto.devops.pipeline.client.model.release.vo.*;
import com.zto.devops.pipeline.client.model.release.vo.AppStopDetailVO;
import com.zto.devops.pipeline.client.model.flow.entity.FlowRelatedAppAndInstanceVO;
import com.zto.devops.pipeline.client.model.release.vo.ExecutableEventVO;
import com.zto.devops.pipeline.client.model.release.vo.ReleaseEventVO;
import com.zto.devops.pipeline.client.model.release.vo.ReleasePlanVO;
import com.zto.devops.pipeline.client.model.release.vo.apply.WorkOrder;
import com.zto.devops.pipeline.client.model.rpc.middleware.ItemChangeCheckResultDTO;
import com.zto.devops.pipeline.client.model.workorder.WorkOrderOverviewVO;
import com.zto.devops.pipeline.client.service.flow.model.FlowAuditGetReq;
import com.zto.devops.pipeline.client.service.release.model.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/17
 * @Version 1.0
 */
public interface IReleasePlanService {

    Result<Void> execute(ExecuteReleasePlanReq req);

    Result<Void> initReleasePlan(InitReleasePlanReq req);

    Result<ReleasePlanVO> findReleasePlan(FindReleasePlanReq req);

    Result<WorkOrderOverviewVO> listReleasePlanWorkOrders(VersionCodeReq req);

    /**
     * 工单申请前置校验
     *
     * @param req
     * @return
     */
    Result<WorkOrderApplyPreCheckResp> preCheckCreateWorkOrder(VersionCodeReq req);

    /**
     * 上线计划提前校验
     * @param req
     * @return
     */
    Result<ApplyPreCheckResp> releasePlanPreCheck(ReleasePreCheckReq req);

    /**
     * 创建上线计划工单
     *
     * @param req
     * @return
     */
    Result<Void> createReleaseWorkOrders(VersionCodeReq req);

    /**
     * 取消发布前置校验
     *
     * @param req
     * @return
     */
    Result<CancelReleasePreCheckResp> preCheckCancelRelease(VersionCodeReq req);

    /**
     * 取消发布
     *
     * @param req
     * @return
     */
    Result<Void> cancelRelease(VersionCodeReq req);

    Result<List<ReleaseEventVO>> listReleasePlanGenericView(ListReleasePlanGenericViewReq req);


    Result<ExecutableEventVO> listExecutableEvents(ListExecutableEventsReq req);

    /**
     * 开始执行
     * @param req
     * @return
     */
    @Deprecated
    Result<ReleasePlanVO> startExecuteReleasePlan(VersionCodeReq req);

    Result<ReleasePlanVO> startExecuteReleasePlan(StartExecuteReleasePlanReq req);

    /**
     * 工单重新提交审核
     * @param req 工单请求对象
     * @return
     */
    Result<Void> retryCreateWorkOrder(RetryCreateWorkOrderReq req);

    Result<ItemChangeCheckResultDTO> findPlanConfigDiffs(FindPlanConfigDiffReq req);

    /**
     * 根据工单获取需要查询工单明细的入参信息
     *
     * @param req
     * @return
     */
    Result<ReleasePlanWorkOrderParamsResp> queryWorkOrderParams(FlowAuditGetReq req);

    /**
     * 获取工单信息
     *
     * @param req
     * @return
     */
    Result<AuditTaskInfoResp> queryAuditTaskInfo(FlowAuditGetReq req);

    /**
     * 设置配置冲突执行策略
     * @param req
     * @return
     */
    Result<Void> confItemConflictStrategy(ConfItemConflictStrategyReq req);


    List<ZbaseApplyViewVO> listWorkOrdersUserByTodo(ApplyQueryParam param);

    PageApplyViewVO listWorkOrdersUserBySubmitAndDone(ApplyQueryParam param);

    Result<FlowApplyReleaseVO> listDeployBatch(ListDeployBatchReq req);

    Result<List<FlowRelatedAppAndInstanceVO>> listDeployInstance(ListDeployInstanceReq req);

    Result<ReleasePlanDeployBatchTagVO> getNextDeployBatch(ReleasePlanDeployBatchTagReq req);

    Result<List<ReleasePlanDeployBatchTagVO>> listDeployBatchTag(ReleasePlanDeployBatchTagReq req);

    Result<Void> preCreatePlanSubWorkOrder(CreateSubWorkOrderReq req);

    Result<List<AppStopDetailVO>> getPreInstanceStopDetail(InstanceStopDetailReq req);

    Result<AppMrVO> listAppMrComment(ListAppMrCommentReq req);

    Result<List<User>> listMrCommentators(ListAppMrCommentatorReq req);

    Result<ReleasePlanMergeConflictDetail> listReleasePlanMergeConflicts(VersionCodeReq req);

    Result<Void> resolveReleasePlanMergeConflicts(ResolveReleasePlanMergeConflictReq req);

    PageResult<ReleasePlanLogVO> pageOperationLog(PageReleasePlanLogReq req);

}

