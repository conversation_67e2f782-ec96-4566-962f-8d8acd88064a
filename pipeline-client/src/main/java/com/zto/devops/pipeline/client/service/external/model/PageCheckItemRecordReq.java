package com.zto.devops.pipeline.client.service.external.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.framework.common.util.time.DateUtil;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/17
 */
@Data
public class PageCheckItemRecordReq extends PageQueryBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "appId")
    private String appId;

    @ZsmpModelProperty(description = "开始时间",sample = "2025-02-11 16:35:08")
    private Date startTime;

    @ZsmpModelProperty(description = "结束时间", sample = "2025-02-11 16:35:08")
    private Date endTime;

    @ZsmpModelProperty(description = "产品名称", sample = "")
    private String productName;

    @ZsmpModelProperty(description = "部门名称", sample = "")
    private String deptName;

    @ZsmpModelProperty(description = "检查结果", sample = "true-成功，false-失败")
    private Boolean checkResult;
}
