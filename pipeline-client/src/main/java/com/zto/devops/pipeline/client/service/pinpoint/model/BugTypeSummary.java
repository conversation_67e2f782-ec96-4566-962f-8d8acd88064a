package com.zto.devops.pipeline.client.service.pinpoint.model;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: cher
 * @Date: 2023/6/2 13:21
 **/
@Data
public class BugTypeSummary extends BugSeveritySummary implements Serializable {

    private static final long serialVersionUID = 1L;
    private String bugType;
    private String bugTypeId;
//    private Integer heightIssueCount = 0;
//    private Integer middleIssueCount = 0;
//    private Integer lowIssueCount = 0;

    private Integer id;//主键
    private String bugTypeCn;
//    private String bugTypeId;
    private String parentBugTypeCn;
    private String title; // 名称
    private String bugPath; //缺陷所在的文件路径
    private String fullFile; //缺陷所在的文件完整路径
    private Integer lineNum;//缺陷所在的行号
    private Integer severity;
    private String severityDesc;
    private Integer step;//缺陷调用步骤
    private String pinpointIssueId;//缺陷ID
    //    private Date createTime;// 缺陷第一次上传的时间
    private String hashId;  //缺陷唯一ID（根据相似度进行聚类）
    //    private Date modifyTime;// 缺陷最新上传的时间
    private String pinpointProjectId; //项目的ID
    private String repId; //批次
    private Integer pinpointReview; //标注
    private Integer pinpointStatus; // 状态
    private String pinpointStatusDesc;
//    private String sourceCodeAndReferenceUrl;
//    private String sourceTraceUrl;
    private String detailUrl;
    private List<BugTypeSummary> children = new ArrayList<>();
//    private List<PinpointIssueVO> pinpointIssueList;

    // 前端使用
    private String code;
    private Integer level;
}
