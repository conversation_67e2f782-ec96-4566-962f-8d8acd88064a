package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author：chenguangyu
 * @Description：
 * @Date：2025/6/30 14:37
 */
@Data
@ZsmpModel(description = "查询加固策略req")
public class ReinforceStrategyReq implements Serializable {
    @Auth(type = AuthTypeConstant.APPLICATION)
    @GatewayModelProperty(description = "应用编码")
    private String code;
}
