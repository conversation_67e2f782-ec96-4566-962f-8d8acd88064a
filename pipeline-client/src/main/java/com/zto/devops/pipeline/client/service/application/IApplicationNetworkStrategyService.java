package com.zto.devops.pipeline.client.service.application;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.application.entity.strategy.AppNetworkStrategyVO;
import com.zto.devops.pipeline.client.service.application.model.strategy.*;

import java.util.List;

/**
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Description: 应用网络策略
 * @Date: 2024/7/11 10:50
 */
public interface IApplicationNetworkStrategyService {

    /**
     * 申请应用网络策略
     */
    Result<Void> addAppNetworkStrategy(AddAppNetworkStrategyReq req);

    /**
     * 删除应用网络策略
     */
    Result<Void> deleteAppNetworkStrategy(DeleteAppNetworkStrategyReq req);

    /**
     * 重新申请应用网络策略
     */
    Result<Void> reAddAppNetworkStrategy(ReAddAppNetworkStrategyReq req);

    /**
     * 重试应用网络策略
     */
    Result<Void> retryAppNetworkStrategy(RetryAppNetworkStrategyReq req);

    /**
     * 应用网络策略列表查询
     */
    Result<List<AppNetworkStrategyVO>> listAppNetworkStrategy(AppNetworkStrategyListReq req);


    /**
     * 查询同批次应用网络策略
     */
    Result<ListAppNetworkStrategyGroupResp> listAppNetworkStrategyGroup(ListAppNetworkStrategyGroupReq req);

    /**
     * 调zke的新增接口，重新生成网络策略
     */
    Result<Void> reFreshZkeNetworkStrategy(CreateZkeNetworkStrategyReq req);

    /**
     * 获取环境可用性状况
     */
    Result<List<EvnUsableStateResp>> listEvnUsableState(ListEvnUsableStateReq req);

    /**
     * 获取公网出口IP
     */
    Result<List<PublicNetworkEgressIpResp>> listPublicNetworkEgressIp(ListPublicNetworkEgressIpReq req);
}
