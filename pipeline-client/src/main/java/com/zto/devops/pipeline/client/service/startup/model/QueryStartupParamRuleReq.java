package com.zto.devops.pipeline.client.service.startup.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * startup param req
 *
 * <AUTHOR>
 * @date 2025-04-10 15:53
 */
@Data
public class QueryStartupParamRuleReq implements Serializable {
    /**
     * 查询条件关键字
     */
    @GatewayModelProperty(description = "查询条件关键字", sample = "keyword", required = false)
    private String keyword;
}
