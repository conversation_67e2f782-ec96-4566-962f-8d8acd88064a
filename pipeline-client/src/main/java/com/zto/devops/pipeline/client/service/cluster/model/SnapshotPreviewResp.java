package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.devops.pipeline.client.model.cluster.entity.SnapshotLineVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SnapshotPreviewResp implements Serializable {
    private static final long serialVersionUID = 1L;
    @GatewayModelProperty( description = "应用编码", sample = "应用编码")
    private String applicationCode;
    @GatewayModelProperty( description = "应用名称", sample = "应用名称")
    private String applicationName;
    @GatewayModelProperty( description = "快照明细", sample = "[]")
    private List<SnapshotLineVO> snapshotLines;
}
