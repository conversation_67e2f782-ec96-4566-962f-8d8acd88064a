package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2022/9/13
 * @Version 1.0
 */
@Data
public class CreateMrReq implements Serializable {

    private static final long serialVersionUID = 1L;


    @Auth(type = AuthTypeConstant.APPLICATION)
    @GatewayModelProperty(description = "应用编码")
    private String code;

    @GatewayModelProperty(description = "合并请求标题")
    private String title;

    @GatewayModelProperty(description = "描述", required = false)
    private String description;

    @GatewayModelProperty(description = "原分支名")
    private String sourceBranch;

    @GatewayModelProperty(description = "目标分支名")
    private String targetBranch;

    @GatewayModelProperty(description = "审核人",required = false)
    private List<User> reviewers;  //发宝盒消息  存redis  入库  不推给git
}
