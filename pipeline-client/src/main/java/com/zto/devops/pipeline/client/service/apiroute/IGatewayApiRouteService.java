package com.zto.devops.pipeline.client.service.apiroute;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.apiroute.entity.GatewayApiRouteInfoVO;
import com.zto.devops.pipeline.client.service.apiroute.model.*;

import java.util.List;

/**
 * 流量网关/api网关接口
 *
 * <AUTHOR>
 * @date 2024-10-23 17:05
 **/
public interface IGatewayApiRouteService {
    /**
     * 此接口只用于新增流量网关路由配置
     * @param req req
     * @return void
     */
    Result<Void> addGatewayApiRoute(AddGatewayApiRouteReq req);

    /**
     * 发布域，api网关/流量网关列表查询
     *
     * @param req req
     * @return result
     */
    PageResult<GatewayApiRouteInfoVO> listGatewayApiRouteByProduct(ListGatewayApiRouteReq req);

    /**
     * 更新/删除api网关/流量网关配置
     * @param req req
     * @return void
     */
    Result<Void> mangeGatewayApiRoute(ManageGatewayApiRouteReq req);

    /**
     * 查询产品下的网关的域名对应的网关信息
     * @param req req
     * @return list
     */
    Result<List<GatewayDomainInfoResp>> listGatewayDomainByProductCode(GatewayDomainInfoReq req);

    /**
     * 给网关调用，返回哪些配置了路由，网关进行实际的路由
     *
     * @return list
     */
    Result<List<GatewayApiRouteInfoVO>> listGatewayApiRouteConfig();
}