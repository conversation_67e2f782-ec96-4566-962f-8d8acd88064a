package com.zto.devops.pipeline.client.service.pinpoint.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.BugWhiteTypeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ZsmpModel(description = "添加缺陷白名单入参")
@Data
public class AddBugWhiteReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @ZsmpModelProperty(description = "产品code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "缺陷ID", required = true)
    private String pinpointIssueId;

    @ZsmpModelProperty(description = "批次ID", required = true)
    private String repId;

    @ZsmpModelProperty(description = "缺陷白名单类型", required = true)
    private BugWhiteTypeEnum bugWhiteType;
}
