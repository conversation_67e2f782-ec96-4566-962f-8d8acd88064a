package com.zto.devops.pipeline.client.service.contingency.model;


import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.contingency.ContingencyPlanLevelEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
/**
 * @Author: cheng<PERSON>yu
 * @Description:
 * @Date: 2025/3/31 16:35
 */
@Data
@ZsmpModel(description = "新增预案req")
public class AddContingencyPlanReq implements Serializable {

    @Auth(type = AuthTypeConstant.PRODUCT)
    @ZsmpModelProperty(description = "所属产品Code", required = true)
    private String productCode;
    @ZsmpModelProperty(description = "预案名称", required = true)
    private String name;
    @ZsmpModelProperty(description = "预案描述", required = false)
    private String description;
    @ZsmpModelProperty(description = "业务影响", required = true)
    private ContingencyPlanLevelEnum contingencyPlanLevel;
}
