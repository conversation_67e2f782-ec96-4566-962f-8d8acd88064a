package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.zsmp.annotation.gateway.GateWayAliasFor;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/8/31
 * @Version 1.0
 */
@Data
@GatewayModel(description = "实例查询日志入参实体")
public class LogUrlReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实例编码
     */
    @GatewayModelProperty(description = "实例编码")
    @NotBlank(message = "实例编码不能为空")
    private String code;

    @GateWayAliasFor(value = "$req.http.heads.cookie")
    private String cookie;
}
