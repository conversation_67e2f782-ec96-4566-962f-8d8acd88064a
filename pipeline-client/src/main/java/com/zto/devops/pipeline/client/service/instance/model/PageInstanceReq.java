package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PageInstanceReq extends PageQueryBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.CLUSTER)
    @ZsmpModelProperty(description = "集群编码", required = true)
    @NotBlank(message = "集群编码不能为空")
    private String clusterCode;
    @ZsmpModelProperty(description = "实例名称")
    private String instanceName;
    @ZsmpModelProperty(description = "实例ip")
    private String instanceIp;
    @ZsmpModelProperty(description = "实例标签")
    private String instanceTag;
    @ZsmpModelProperty(description = "实例编码集合")
    private List<String> instanceCodes;
}
