package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.plan.ReleaseCheckRuleScopeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@ZsmpModel(description = "上线计划检查入参")
@Data
public class ReleasePreCheckReq implements Serializable {
    @ZsmpModelProperty(description = "版本编码", required = true)
    private String versionCode;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @ZsmpModelProperty(description = "应用code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "校验范围", required = true)
    private ReleaseCheckRuleScopeEnum checkScope;
}
