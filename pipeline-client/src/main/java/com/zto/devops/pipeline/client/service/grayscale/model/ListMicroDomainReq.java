package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * list micro domain req
 *
 * <AUTHOR>
 * @date 2023-04-07 10:24
 */
@Data
public class ListMicroDomainReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 空间标签
     */
    @ZsmpModelProperty(description = "空间标签", required = true)
    private String namespaceTag;

    /**
     * appId列表
     */
    @ZsmpModelProperty(description = "appId列表", required = true)
    private List<String> appIds;
}
