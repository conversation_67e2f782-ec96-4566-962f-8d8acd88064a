package com.zto.devops.pipeline.client.service.application.model.strategy;

import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2024/1/13
 */
@Data
public class PublicNetworkEgressIpResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "吴兴机房", required = false)
    private String dcName;

    @ZsmpModelProperty(description = "ip", required = false)
    private String ip;
}
