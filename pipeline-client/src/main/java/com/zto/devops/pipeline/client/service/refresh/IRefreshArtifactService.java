package com.zto.devops.pipeline.client.service.refresh;

import com.zto.devops.framework.client.dto.Result;

import java.util.List;

/**
 * @Author: cher
 * @Date: 2023/10/25 14:02
 **/
public interface IRefreshArtifactService {

    /**
     *
     * 刷新制品flowCode
     * @return
     */
    Result<Integer> refreshArtifactFlowCode(List<String> appIdList);

    /**
     *
     * 刷新制品初次部署时间
     * @return
     */
    Result<Integer> refreshArtifactFirstDeploymentTime(List<String> productCodes, List<String> flowCodes);
}
