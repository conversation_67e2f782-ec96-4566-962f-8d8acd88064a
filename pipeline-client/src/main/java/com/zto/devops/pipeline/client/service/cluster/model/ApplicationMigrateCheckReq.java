package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.model.application.entity.ApplicationMigrateVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class ApplicationMigrateCheckReq implements Serializable {

    @GatewayModelProperty( description = "应用编码")
    private List<String> applicationCodes;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty( description = "原产品编码")
    private String productCode;
}
