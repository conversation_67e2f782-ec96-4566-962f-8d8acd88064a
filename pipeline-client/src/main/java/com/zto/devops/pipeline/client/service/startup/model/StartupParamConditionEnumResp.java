package com.zto.devops.pipeline.client.service.startup.model;

import com.zto.devops.pipeline.client.model.common.vo.EnumRelateVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * StartupParamConditionEnumResp
 *
 * <AUTHOR>
 * @date 2025-04-14 10:38
 */
@Data
public class StartupParamConditionEnumResp implements Serializable {

    /**
     * 返回枚举值列表
     */
    private List<EnumRelateVO> StartupParamConditionEnumList;


}
