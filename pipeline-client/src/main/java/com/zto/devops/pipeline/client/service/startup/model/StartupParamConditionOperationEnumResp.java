package com.zto.devops.pipeline.client.service.startup.model;

import com.zto.devops.pipeline.client.model.common.vo.EnumRelateVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * StartupParamConditionOperationEnumResp
 *
 * <AUTHOR>
 * @date 2025-04-14 10:57
 */
@Data
public class StartupParamConditionOperationEnumResp implements Serializable {
    /**
     * 条件涉及的操作符对应的vo集合
     */
    private List<EnumRelateVO> startupParamConditionOperationEnumList;
}
