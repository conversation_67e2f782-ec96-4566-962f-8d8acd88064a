package com.zto.devops.pipeline.client.service.pipeline;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.apply.entity.ZbaseApplyViewVO;
import com.zto.devops.pipeline.client.model.pipeline.entity.*;
import com.zto.devops.pipeline.client.service.pipeline.model.*;
import com.zto.devops.pipeline.client.service.release.model.ApplyQueryParam;
import com.zto.devops.pipeline.client.service.release.model.PageApplyViewVO;

import java.util.List;
import java.util.Set;
import java.util.Map;

/**
 * pipeline service
 *
 * <AUTHOR>
 * @date 2023-04-06 17:58
 **/
public interface IPipelineService {

    /**
     * 对外-根据appId查询版本信息
     *
     * @param req req
     * @return resp
     */
    PageResult<MirrorInfoResp> getMirrorInfoByAppId(MirrorInfoReq req);

    /**
     * @param req
     * @return
     */
//    Result<ArtifactDownloadUrlResp> downloadUrl(ArtifactDownloadUrlReq req);

    /***
     * 流水线-运行历史-分页查询
     * */
    PageResult<PipelineExecutionVO> pagePipelineExecution(PipelineExecutionReq req);

    /**
     * 流水线-运行详情接口
     * */
    Result<PipelineExecutionDetailVO> getPipelineExecutionDetail(PipelineExecutionCodeReq req);

    /**
     * 流水线-运行-构建节点基础信息接口
     * */
    Result<PipelineExecutionNodeBuildBaseInfoVO> getPipelineExecutionBuildNodeBaseInfo(PipelineExecutionNodeCodeReq req);

    /**
     * 流水线-运行-部署节点基础信息接口
     * */
    Result<PipelineExecutionNodeBaseInfoVO> getPipelineExecutionDeployNodeBaseInfo(PipelineExecutionNodeCodeReq req);

    /**
     * 流水线-配置-分页查询
     */
    PageResult<PipelineConfigVO> pagePipelineConfig(PagePipelineConfigReq req);

    PageResult<PipelineConfigSimpleVO> pageSimplePipelineConfig(PagePipelineConfigReq req);

    /**
     * 新增流水线定义
     */
    Result<Void> addPipelineConfig(AddPipelineConfigReq req);

    /**
     * 新增或者更新流水线定义
     */
    Result<Void> saveOrUpdatePipelineConfig(SaveOrUpdatePipelineConfigReq req);

    /**
     * 删除流水线定义前置验证
     */
    Result<Void> deletePipelineConfigPreCheck(String configCode);

    /**
     * 删除流水线定义
     */
    Result<Void> deletePipelineConfig(String configCode);

    Result<Void> resetPipelineConfigNodeSwitch(ResetPipelineConfigNodeSwitchReq req);

    /**
     * 流水线定义详情查询
     */
    Result<PipelineConfigDetailVO> getPipelineConfigDetail(GetPipelineConfigDetailReq req);

    /**
     * 流水线定义详情预览
     */
    Result<PipelineConfigDetailVO> getPipelineConfigDetailForPreview(GetPipelineConfigDetailReq req);

    /**
     * 拷贝流水线定义
     */
    Result<PipelineConfigDetailVO> copyPipelineConfig(CopyPipelineConfigReq req);

    /**
     * 刷新节点属性包括卡点和单元测试、代码缺陷扫描
     */
//    Result<Integer> refreshPipelineConfigParameter(List<String> pipelineConfigCodeList);

    /**
     * 新增人工确认节点
     */
//    Result<Integer> refreshPipelineConfigNodeAndLink(List<String> pipelineConfigCodeList);

    /**
     * 刷新回归阶段节点(移除)
     */
    Result<Integer> refreshRegressPipelineConfigNodeAndLink(List<String> pipelineConfigCodeList);

    /**
     * 刷新代码缺陷扫描节点
     */
    Result<Integer> refreshBugScanPipelineConfigNodeAndLink(List<String> pipelineConfigCodeList);

    /**
     * 版本详情-流水线
     */
//    Result<PipelineGlobalFlowchartVO> getPipelineGlobalFlowchart(String versionCode);

    PipelineConfigVO getPipelineConfigByCode(String configCode);//project

    List<PipelineConfigSimpleVO> getPipelineConfigsByProductCode(String productCode);

    com.zto.titans.common.entity.Result<Void> updateApplicationAlerter(List<ApplicationAlerterUpdateVO> alerterUpdateVOS);

    Result<List<String>> queryPipelineConfigName(String configName);


    List<PipelineConfigSimpleVO> getByProductCodeAndName(String productCode,List<String> pipelineConfigNames);

    PageResult<PipelineConfigVO> pagePipelineConfigTemplate(PagePipelineConfigReq req);

    /**
     * 获取系统流水线
     *
     * @param productCodes
     * @return
     */
    List<PipelineConfigSimpleVO> getSystemPipelineConfigs(Set<String> productCodes);

    Result<Map> getOperatorList(String productCode);

    List<ZbaseApplyViewVO> listWorkOrdersUserByTodo(ApplyQueryParam param);

    PageApplyViewVO listWorkOrdersUserBySubmitAndDone(ApplyQueryParam param);

    /**
     * 移动端应用-构建记录-分页查询
     * @param req 版本编号、应用编号、产品编号、环境、构建包类型
     * @return 构建记录分页结果
     */
    PageResult<MobileAppBuildExecutionVO> pageMobileAppBuildExecution(MobileAppBuildExecutionReq req);


    Result<MobileBuildButtonsVO> queryMobileBuildStatus(MobileBuildButtonsReq req);
}