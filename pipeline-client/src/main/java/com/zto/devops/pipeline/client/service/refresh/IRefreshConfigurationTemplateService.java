package com.zto.devops.pipeline.client.service.refresh;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.service.refresh.model.UpdateConfigurationTemplateReq;

/**
 * @Author: cher
 * @Date: 2023/10/25 14:02
 **/
public interface IRefreshConfigurationTemplateService {

    /**
     * 更新ConfigurationTemplate
     *
     * @return
     */
    Result<Void> updateConfigurationTemplate(UpdateConfigurationTemplateReq req);

    Result<Void> agentGroupAddStatus();
}
