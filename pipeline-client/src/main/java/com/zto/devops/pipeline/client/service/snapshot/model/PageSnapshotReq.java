package com.zto.devops.pipeline.client.service.snapshot.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.devops.pipeline.client.enums.SnapshotSourceEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * page snapshot req
 *
 * <AUTHOR>
 * @date 2023-03-16 15:11
 */
@Getter
@Setter
public class PageSnapshotReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 1L;
    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;
    @GatewayModelProperty(description = "名称", required = false)
    private String name;
    @GatewayModelProperty(description = "namespace", required = false)
    private List<String> namespaceList;
    @GatewayModelProperty(description = "环境", required = false)
    private List<EnvEnum> envList;
    @GatewayModelProperty(description = "快照来源", required = false)
    private List<SnapshotSourceEnum> sourceList;
    @GatewayModelProperty(description = "快照生产开始时间", required = false)
    private Date startTime;
    @GatewayModelProperty(description = "快照生产结束时间", required = false)
    private Date endTime;
}
