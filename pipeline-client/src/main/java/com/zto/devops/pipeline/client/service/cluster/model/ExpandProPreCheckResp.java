package com.zto.devops.pipeline.client.service.cluster.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/9/2
 */
@Data
public class ExpandProPreCheckResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检查结果
     */
    private Boolean checkResult;

    /**
     * 历史最大进程数
     */
    private Integer historyNum;

    /**
     * 增至的进程总数
     */
    private Integer addToTotalNum;

    /**
     * 生产进程总数可扩容的比率
     */
    private Double proProcessNumRatio;
}
