package com.zto.devops.pipeline.client.service.external;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.service.external.model.*;

import java.util.List;

/**
 * @Author: cher
 * @Date: 2023/12/26 17:30
 * 对外接口
 **/
public interface IPipelineExternalService {

    /**
     * 查询动态多环境版本
     *
     * @param req
     * @return
     */
    Result<List<ListAutoEnvVersionResp>> listAutoEnvVersions(ListAutoEnvVersionReq req);

    /**
     * 获取多环境配置信息
     *
     * @return
     */
    Result<AutoEnvSdkConfigResp> getAutoEnvSdkConfig();

    /**
     * 获取预发环境部署版本信息
     *
     * @return
     */
    Result<List<PreDeployVersionResp>> preDeployVersion();

    /**
     * 查询测试环境部署空间的ossTag
     *
     * @param req
     * @return
     */
    Result<DeployNamespaceOssTagResp> queryLatestFatDeployNamespaceOssTag(DeployNamespaceOssTagReq req);

    /**
     * 更新用户版本tag
     *
     * @param req
     * @return
     */
    Result<Void> updateUserVersionTag(UpdateUserVersionTagReq req);

    /**
     * 获取用户版本tag
     *
     * @return
     */
    Result<UserVersionTagResp> queryUserVersionTag();

    /**
     * 获取所有用户版本tag
     *
     * @return
     */
    Result<List<UserVersionTagResp>> listUserVersionTag();

    /**
     * 删除用户版本tag
     *
     * @return
     */
    Result<Void> deleteUserVersionTag();

    /**
     * 检查maven制品是否已上生产
     * @return 是否上过生产
     */
    Result<Boolean> queryArtifactOnlineStatus(QueryArtifactOnlineStatusReq req);

    /**
     * 检查项留痕
     * @return CheckItemRecordResp
     */
    PageResult<CheckItemRecordResp> pageCheckItemRecord(PageCheckItemRecordReq req);
    /**
     * 查询核心应用appId集合
     * */
    Result<List<String>> listCoreAppId();
}
