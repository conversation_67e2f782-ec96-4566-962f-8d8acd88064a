package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GateWayAliasFor;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/1/4
 * @Version 1.0
 */
@Data
public class SearchInstanceReq implements Serializable {

    private static final long serialVersionUID = 1L;


    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    @NotBlank(message = "产品编码不能为空")
    private String productCode;

    @GatewayModelProperty(description = "搜索关键字")
    @NotBlank(message = "搜索关键字不能为空")
    private String key;

    @GateWayAliasFor(value = "$req.http.heads.cookie")
    private String cookie;
}
