package com.zto.devops.pipeline.client.service.checkItem.model;

import com.zto.devops.pipeline.client.enums.ProductRoleEnum;
import com.zto.devops.pipeline.client.enums.flow.CheckActionEnum;
import com.zto.devops.pipeline.client.enums.flow.CheckItemTypeEnum;
import com.zto.devops.pipeline.client.enums.flow.CheckRecordTypeEnum;
import com.zto.devops.pipeline.client.enums.flow.CheckTypeEnum;
import com.zto.devops.pipeline.client.model.checkItem.CheckItemTimeLevel;
import com.zto.devops.pipeline.client.model.checkItem.CheckScope;
import com.zto.devops.pipeline.client.model.checkItem.entity.CheckFailedNoticeAddress;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/30
 */
@ZsmpModel(description = "新增检查项入参")
@Data
public class AddCheckItemReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "检查项名称", sample = "分支检查", required = true)
    private String checkItemName;

    @ZsmpModelProperty(description = "类型", sample = "CHECK", required = true)
    private CheckItemTypeEnum itemType;

    @ZsmpModelProperty(description = "下发事项类型", sample = "IMPROVE_RD_REQUIREMENT")
    private CheckRecordTypeEnum recordType;

    @ZsmpModelProperty(description = "提出人编码", required = true)
    private Long introducerId;

    @ZsmpModelProperty(description = "提出人", required = true)
    private String introducer;

    @ZsmpModelProperty(description = "事项接受角色", sample = "DEVELOPER_OWNER")
    private ProductRoleEnum relateRole;

    @ZsmpModelProperty(description = "成功标题", sample = "分支检查正常", required = true)
    private String successTitle;

    @ZsmpModelProperty(description = "失败标题", sample = "分支检查异常", required = true)
    private String failTitle;

    @ZsmpModelProperty(description = "检查项规则", sample = "BRANCH_CHECK", required = true)
    private CheckTypeEnum checkRule;

    @ZsmpModelProperty(description = "检查参数", sample = "{}")
    private String checkRuleContext;

    @ZsmpModelProperty(description = "触发动作", sample = "APPLY_RELEASE", required = true)
    private CheckActionEnum checkTriggerAction;

    @ZsmpModelProperty(description = "正向作用域", sample = "[]", required = true)
    private List<CheckScope> forwardScopes;

    @ZsmpModelProperty(description = "反向作用域", sample = "[]")
    private List<CheckScope> reverseScopes;

    @ZsmpModelProperty(description = "检查项目时间与等级关系", sample = "[]")
    private List<CheckItemTimeLevel> timeLevels;

    @ZsmpModelProperty(description = "检查项描述")
    private String checkItemDesc;

    @ZsmpModelProperty(description = "检查失败通知群地址", sample = "[]")
    private List<CheckFailedNoticeAddress>  checkFailedNoticeAddresses;
}
