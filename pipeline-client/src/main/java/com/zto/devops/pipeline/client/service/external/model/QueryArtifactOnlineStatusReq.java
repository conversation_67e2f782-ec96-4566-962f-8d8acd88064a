package com.zto.devops.pipeline.client.service.external.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/12/20 13:26
 */
@Data
@ZsmpModel(description = "动态多环境sdk配置出参")
public class QueryArtifactOnlineStatusReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "maven artifactId", required = true)
    private String artifactId;

    @ZsmpModelProperty(description = "maven groupId", required = true)
    private String groupId;

    @ZsmpModelProperty(description = "maven version", required = true)
    private String version;
}
