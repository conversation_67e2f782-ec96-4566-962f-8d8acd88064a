package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/10/18
 */
@ZsmpModel(description = "工单申请前置校验入参")
@Data
public class WorkOrderApplyPreCheckReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @ZsmpModelProperty(description = "应用code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "版本编码", required = true)
    private String versionCode;
}
