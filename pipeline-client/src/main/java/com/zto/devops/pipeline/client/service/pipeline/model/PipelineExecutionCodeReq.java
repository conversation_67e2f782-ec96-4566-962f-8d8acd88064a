package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @Date: 2023/6/28 14:53
 */
@ZsmpModel(description = "流水线-运行详情入参实体")
@Data
public class PipelineExecutionCodeReq implements Serializable {

    @ZsmpModelProperty(description = "流水线编码", sample = "1", required = true)
    private String executionCode;
}
