package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description: 查询中通云主机镜像入参
 * @Author: cher
 * @Date: 2022/1/6 13:33
 **/
@Data
@GatewayModel(description = "查询中通云主机镜像实体")
public class QueryZtoCloudImageReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    /**
     * 主机来源ID
     */
    @GatewayModelProperty(description = "主机来源ID")
    @NotBlank(message = "主机来源ID不能为空")
    private String src_id;

    /**
     * 环境
     */
    @GatewayModelProperty(description = "环境")
    @NotNull(message = "环境不能为空")
    private EnvEnum env;
}
