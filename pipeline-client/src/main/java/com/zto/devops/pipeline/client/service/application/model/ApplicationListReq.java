package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 2021/7/26
 */

@Data
public class ApplicationListReq extends PageQueryBase implements Serializable {
    /**
     * 所属产品id
     */
    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品id",required = false)
    private String productCode;

    /**
     * 应用类型id
     */
    @GatewayModelProperty(description = "应用类型id",required = false)
    private List<String> applicationTypeCode;

    /**
     * 应用名
     */
    @GatewayModelProperty(description = "应用id",required = false)
    private String name;

    /**
     * 应用id
     */
    @GatewayModelProperty(description = "应用id",required = false)
    private String appId;

    /**
     * 负责人userId
     */
    @GatewayModelProperty(description = "负责人userId",required = false)
    private List<Long> alerterIds;

    @GatewayModelProperty(description = "是否白名单",required = false)
    private List<Boolean> whiteList;
}
