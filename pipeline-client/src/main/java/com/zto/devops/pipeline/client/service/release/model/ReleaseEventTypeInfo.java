package com.zto.devops.pipeline.client.service.release.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 列出可选择的事件类型响应
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class ReleaseEventTypeInfo implements Serializable {

    @GatewayModelProperty(description = "事件类型枚举值")
    private String eventType;

    @GatewayModelProperty(description = "事件类型名称")
    private String eventName;
} 