package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.devops.pipeline.client.enums.PlanStatusEnum;
import com.zto.devops.pipeline.client.model.simple.ArtifactDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 返回值
 *
 * <AUTHOR>
 * @date 2023-09-28 14:12
 */
@Data
public class QueryPlanResp implements Serializable {
    private PlanStatusEnum planStatus;

    private List<ArtifactDO> artifactList;
}
