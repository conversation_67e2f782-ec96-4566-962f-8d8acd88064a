package com.zto.devops.pipeline.client.service.release.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
public class RetryCreateWorkOrderReq implements Serializable {
    @GatewayModelProperty(description = "工单编号")
    private String workOrderCode;

    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "版本编码")
    private String versionCode;
}
