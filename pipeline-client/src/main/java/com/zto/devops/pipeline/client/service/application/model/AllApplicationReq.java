package com.zto.devops.pipeline.client.service.application.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 2021/10/11
 */
@Data
public class AllApplicationReq implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "应用类型集合", required = false)
    private List<String> applicationTypeCodes;

    @GatewayModelProperty(description = "appid集合", required = false)
    private List<String> appIds;
}
