package com.zto.devops.pipeline.client.service.startup.model;

import com.zto.devops.pipeline.client.enums.InstanceStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * DiffCheckStartupParamCommand
 *
 * <AUTHOR>
 * @date 2025-04-16 15:20
 */
@Data
public class DiffCheckStartupParamReq implements Serializable {
    @GatewayModelProperty(description = "产品编码",required = false)
    private String productCode;

    @GatewayModelProperty(description = "实例状态",required = false)
    private List<InstanceStatusEnum> statusList;

    @GatewayModelProperty(description = "是否全量比对",required = false)
    private Boolean diffCheckAll;



}
