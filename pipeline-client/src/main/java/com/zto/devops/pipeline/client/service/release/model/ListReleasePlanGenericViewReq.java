package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.pipeline.client.model.release.enums.ReleaseEventType;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 获取执行状态下的总览
 * @Date 2024/8/8
 * @Version 1.0
 */
@Data
public class ListReleasePlanGenericViewReq implements Serializable {
    @GatewayModelProperty(description = "版本编码")
    private String versionCode;

    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "事件类型: PUBLISH/ROLLBACK", required = false)
    private String eventType;

    @GatewayModelProperty(description = "上线计划快照编码", required = false)
    private String releasePlanSnapshotCode;

    public ReleaseEventType eventType() {
        if (StringUtil.isEmpty(eventType) || ReleaseEventType.PUBLISH.name().equals(eventType)) {
            return ReleaseEventType.PUBLISH;
        }
        if (ReleaseEventType.ROLLBACK.name().equals(eventType)) {
            return ReleaseEventType.ROLLBACK;
        }
        throw new ServiceException("不支持的事件类型:" + eventType);
    }
}
