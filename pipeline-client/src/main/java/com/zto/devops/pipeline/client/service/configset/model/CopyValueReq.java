package com.zto.devops.pipeline.client.service.configset.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CopyValueReq implements Serializable {

    @GatewayModelProperty(description = "所属key的code")
    private String configSetKeyCode;

    @GatewayModelProperty(description = "规则的值")
    private String configSetValue;

    @GatewayModelProperty(description = "类型",sample = "1-灰度，2-全网")
    private Integer grayType;

    @GatewayModelProperty(description = "描述",required = false)
    private String description;

    @GatewayModelProperty(description = "规则脚本",required = false)
    private String grayScript;

    @GatewayModelProperty(description = "uiData",required = false)
    private String uiData;

    @GatewayModelProperty(description = "百分比", sample = "1", required = false)
    private Integer grayRatio;

}
