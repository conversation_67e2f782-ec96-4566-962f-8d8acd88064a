package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.pipeline.client.model.release.enums.ReleasePlanActionEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/17
 * @Version 1.0
 */
@Data
public class FindReleasePlanReq implements Serializable {

    private String productCode;

    private String versionCode;

    @GatewayModelProperty(description = "进入到上线计划，目标动作是什么", required = false)
    private ReleasePlanActionEnum targetAction;

    @GatewayModelProperty(description = "是否审核页面", required = false)
    private boolean onlyForAuditView = false;

    @GatewayModelProperty(description = "执行的快照编码", required = false)
    private String releasePlanSnapshotCode;
}
