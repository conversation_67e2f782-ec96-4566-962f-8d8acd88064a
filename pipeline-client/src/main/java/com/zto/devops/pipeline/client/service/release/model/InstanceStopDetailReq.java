package com.zto.devops.pipeline.client.service.release.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;

@Data
public class InstanceStopDetailReq implements Serializable {

    @ZsmpModelProperty(description = "版本编码", required = false)
    private String versionCode;

    @ZsmpModelProperty(description = "事件编码", required = false)
    private String eventCode;
}
