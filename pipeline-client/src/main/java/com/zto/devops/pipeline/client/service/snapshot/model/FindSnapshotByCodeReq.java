package com.zto.devops.pipeline.client.service.snapshot.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * find snapshot by code req
 *
 * <AUTHOR>
 * @date 2023-03-16 14:16
 */
@Getter
@Setter
public class FindSnapshotByCodeReq implements Serializable {
    private static final long serialVersionUID = 1L;
    @Auth(type = AuthTypeConstant.NAMESPACE_SNAPSHOT)
    @GatewayModelProperty( description = "快照编码", sample = "NS202101010001")
    private String code;
}
