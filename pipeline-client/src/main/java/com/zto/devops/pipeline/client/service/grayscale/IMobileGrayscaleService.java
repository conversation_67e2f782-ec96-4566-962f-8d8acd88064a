package com.zto.devops.pipeline.client.service.grayscale;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.grayscale.entity.GrayscaleVO;
import com.zto.devops.pipeline.client.model.grayscale.entity.mobile.MobileDetailVO;
import com.zto.devops.pipeline.client.service.grayscale.model.PageGrayscaleReq;
import com.zto.devops.pipeline.client.service.grayscale.model.mobile.MobileDetailReq;
import com.zto.devops.pipeline.client.service.grayscale.model.mobile.MobileGrayscaleReq;
import com.zto.devops.pipeline.client.service.grayscale.model.mobile.MobileOfflineReq;
import com.zto.devops.pipeline.client.service.grayscale.model.mobile.MobileOnlineReq;

/**
 * @Author：chenguangyu
 * @Description：移动端灰度服务实现
 * @Date：2025/7/8 9:25
 */
public interface IMobileGrayscaleService {

    /**
     * 编辑发布移动端(安卓、ios等)
     * */
    Result<Void> editPublishMobile(MobileGrayscaleReq req);

    /**
     * 上线移动端(安卓、ios等)
     * */
    Result<Void> onlineMobile(MobileOnlineReq req);
    /**
     * 下线移动端(安卓、ios等)
     * */
    Result<Void> offlineMobile(MobileOfflineReq req);

    /**
     * 移动端详情
     * */
    Result<MobileDetailVO> getMobileDetail(MobileDetailReq req);
    /**
     * 移动端灰度列表查询
     * @param req req
     * @return list
     */
    PageResult<GrayscaleVO> pageMobileGrayscaleQuery(PageGrayscaleReq req);

}
