package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.pipeline.client.model.release.enums.ReleaseEventType;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ReleasePlanDeployBatchTagReq implements Serializable {
    @ZsmpModelProperty(description = "事件类型", required = true)
    private ReleaseEventType eventType;

    @ZsmpModelProperty(description = "产品编码", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "版本编码", required = true)
    private String versionCode;
}
