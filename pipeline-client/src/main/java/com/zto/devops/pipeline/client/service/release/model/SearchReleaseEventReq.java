package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.pipeline.client.model.release.enums.ReleaseEventType;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 变更事件搜索请求DTO
 * 
 * <AUTHOR>
 * @Description 包含变更事件搜索的所有参数
 * @Date 2025-01-27
 * @Version 1.0
 */
@Data
public class SearchReleaseEventReq implements Serializable {

    @GatewayModelProperty(description = "产品编码", required = true)
    private String productCode;

    @GatewayModelProperty(description = "当前版本编码", required = true)
    private String currentVersionCode;

    @GatewayModelProperty(description = "变更类型列表", required = false)
    private List<ReleaseEventType> eventTypes;

    @GatewayModelProperty(description = "开始时间", required = false)
    private Date startTime;

    @GatewayModelProperty(description = "结束时间", required = false)
    private Date endTime;

    @GatewayModelProperty(description = "变更人ID列表", required = false)
    private List<Long> transactor;

    @GatewayModelProperty(description = "版本编码列表", required = false)
    private List<String> versionCodes;

    @GatewayModelProperty(description = "是否已应用", required = false)
    private Boolean applied;

    @GatewayModelProperty(description = "是否已上线", required = false)
    private Boolean released;

    @GatewayModelProperty(description = "搜索关键词", required = false)
    private String keyword;

    @GatewayModelProperty(description = "page", required = false)
    private int page;

    @GatewayModelProperty(description = "size", required = false)
    private int size;

    public List<ReleaseEventType> getEventTypes() {
        if (CollectionUtil.isEmpty(eventTypes)) {
            return Collections.emptyList();
        }
        if (eventTypes.stream().anyMatch(ReleaseEventType.RESOURCE_APPLY::equals)) {
            eventTypes.addAll(ReleaseEventType.listSubRealEventTypes(ReleaseEventType.RESOURCE_APPLY.getResourceType()));
        }
        return eventTypes
                .stream()
                .filter(type -> !ReleaseEventType.RESOURCE_APPLY.equals(type))
                .collect(Collectors.toList());
    }
}