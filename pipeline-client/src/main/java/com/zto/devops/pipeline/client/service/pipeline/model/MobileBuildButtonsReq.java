package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 移动端构建按钮状态请求类
 */
@Data
public class MobileBuildButtonsReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 版本编码
     */
    @ZsmpModelProperty(description = "版本编码", sample = "VER25062600801", required = true)
    private String versionCode;

    /**
     * 应用编码
     */
    @ZsmpModelProperty(description = "应用编码", sample = "APP2306288012", required = false)
    private String applicationCode;
}
