package com.zto.devops.pipeline.client.service.refresh.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 产品打开简易流水线提交即构建入参
 * @Author: cher
 * @Date: 2023/10/25 14:32
 **/
@Data
@ZsmpModel(description = "产品打开简易流水线提交即构建入参")
public class ProductOpenPushBuildReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "是否所有产品", sample = "", required = true)
    private Boolean allProduct;

    @ZsmpModelProperty(description = "产品列表", sample = "[]")
    private List<String> productCodes;
}
