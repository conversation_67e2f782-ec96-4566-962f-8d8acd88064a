package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.DeployTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 编辑实例
 * @Author: cher
 * @Date: 2021/7/9 13:37
 **/
@Data
@GatewayModel(description = "编辑实例实体")
public class EditInstanceReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实例编码
     */
    @Auth(type = AuthTypeConstant.INSTANCE)
    @GatewayModelProperty(description = "实例编码")
    @NotBlank(message = "实例编码不能为空")
    private String code;

    /**
     * 实例名称
     */
    @GatewayModelProperty(description = "实例名称", required = false)
    private String name;

    /**
     * 部署参数
     */
    @GatewayModelProperty(description = "部署参数", required = false)
    private String deploymentParam;

//    @GatewayModelProperty(description = "默认部署参数", required = false)
//    private String defaultDeploymentParam;

    /**
     * vm服务端口
     */
    @GatewayModelProperty(description = "vm服务端口", required = false)
    private String port;

    /**
     * vm健康检查端口
     */
    @GatewayModelProperty(description = "vm健康检查端口", required = false)
    private String healthyPort;

    /**
     * 容器基础镜像
     */
    @GatewayModelProperty(description = "容器基础镜像", required = false)
    private String baseImage;

    /**
     * 容器副本数
     */
    @GatewayModelProperty(description = "容器副本数", required = false)
    private String wayneReplicas;

    @GatewayModelProperty(description = "部署类型", required = false)
    private DeployTypeEnum deployType;

    @GatewayModelProperty(description = "实例标签编码集合", required = false)
    private List<String> instanceTagCodeList;

    @GatewayModelProperty(description = "停止命令参数",required = false)
    private String stopParam;
}
