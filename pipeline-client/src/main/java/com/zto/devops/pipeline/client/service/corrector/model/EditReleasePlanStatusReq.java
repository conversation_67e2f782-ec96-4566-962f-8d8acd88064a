package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.devops.pipeline.client.model.release.enums.ReleasePlanStatusEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class EditReleasePlanStatusReq implements Serializable {

    @ZsmpModelProperty(description = "版本号", sample = "VER1234579865", required = true)
    private String versionCode;

    @ZsmpModelProperty(description = "状态", sample = "FAILED", required = true)
    private ReleasePlanStatusEnum status;
}
