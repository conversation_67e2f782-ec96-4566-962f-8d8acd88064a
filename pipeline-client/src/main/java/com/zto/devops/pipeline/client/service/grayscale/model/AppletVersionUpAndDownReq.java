package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.devops.pipeline.client.enums.GrayscaleEventEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ZsmpModel(description = "小程序版本上下线req")
public class AppletVersionUpAndDownReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.APPLICATION)
    @ZsmpModelProperty(description = "应用编码", sample = "APP1", required = true)
    private String applicationCode;

    @ZsmpModelProperty(description = "环境", sample = "FAT", required = true)
    private EnvEnum env;

    @ZsmpModelProperty(description = "版本id", sample = "1", required = true)
    private Long versionId;

    @ZsmpModelProperty(description = "操作", sample = "ONLINE", required = true)
    private GrayscaleEventEnum operate;
}
