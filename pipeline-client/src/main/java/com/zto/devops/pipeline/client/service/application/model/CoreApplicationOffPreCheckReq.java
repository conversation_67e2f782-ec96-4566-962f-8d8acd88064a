package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * core application off req
 *
 * <AUTHOR>
 * @date 2024-03-31 19:02
 */
@Data
public class CoreApplicationOffPreCheckReq implements Serializable {
    @GatewayModelProperty( description = "应用编码")
    private String applicationCode;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty( description = "原产品编码")
    private String productCode;
}
