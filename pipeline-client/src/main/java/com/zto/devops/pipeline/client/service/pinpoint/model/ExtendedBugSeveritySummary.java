package com.zto.devops.pipeline.client.service.pinpoint.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: cher
 * @Date: 2023/6/2 13:21
 **/
@Data
public class ExtendedBugSeveritySummary extends BugSeveritySummary implements Serializable {

    private static final long serialVersionUID = 1L;
    private String applicationCode;
    private String pinpointProjectId;
    private String pinpointReportId;
    private Integer heightIssueCount;
    private Integer middleIssueCount;
    private Integer lowIssueCount;
    private Integer whiteIssueCount;
    private Integer totalIssueCount;

    public void init(){
        heightIssueCount = 0;
        middleIssueCount = 0;
        lowIssueCount = 0;
        whiteIssueCount = 0;
        totalIssueCount = 0;
    }

}
