package com.zto.devops.pipeline.client.service.refresh.model;


import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.BuildTypeEnum;
import com.zto.devops.pipeline.client.enums.DeployTypeEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class UpdateConfigurationTemplateReq  implements Serializable {
    private static final long serialVersionUID = 1L;
    @Auth(type = AuthTypeConstant.SUPPER)
    @ZsmpModelProperty(description = "自增id", sample = "", required = true)
    private Long id;
    @ZsmpModelProperty(description = "参数key", sample = "")
    private String paramKey;
    @ZsmpModelProperty(description = "描述", sample = "")
    private String description;
    @ZsmpModelProperty(description = "默认值", sample = "")
    private String defaultValue;
    @ZsmpModelProperty(description = "目录级别", sample = "")
    private String catalog;
    @ZsmpModelProperty(description = "所属目录名称", sample = "")
    private String catalogName;
    @ZsmpModelProperty(description = "参数类型 1-普通输入框，2-下拉框, 3动态添加, 7多选列表", sample = "")
    private Integer paramType;
    @ZsmpModelProperty(description = "下拉框选项值", sample = "")
    private String paramList;
    @ZsmpModelProperty(description = "输入类型 1非数字类型 2数字类型", sample = "")
    private Integer inputType;
    @ZsmpModelProperty(description = "运行环境类型1容器2虚拟机3zgph5", sample = "")
    private Integer envType;
    @ZsmpModelProperty(description = "构建类型", sample = "")
    private BuildTypeEnum buildType;
    @ZsmpModelProperty(description = "部署类型", sample = "")
    private DeployTypeEnum deployType;
    @ZsmpModelProperty(description = "参数类型 1-必填，0-不必填", sample = "")
    private Integer requiredType;
    @ZsmpModelProperty(description = "参数类型 1-展示，0-不展示", sample = "")
    private Integer isShow;
    @ZsmpModelProperty(description = "排序，从小到大", sample = "")
    private Integer sort;
    @ZsmpModelProperty(description = "输入提示", sample = "")
    private String placeholder;
    @ZsmpModelProperty(description = "输入框最大长度", sample = "")
    private Long inputMaxLength;
    @ZsmpModelProperty(description = "是否未删除", sample = "")
    private Boolean enable;

}