package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.pipeline.client.service.flow.model.MobileAppArtifactVO;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SaveMobileDeployEventBatchReq implements Serializable {

    @ZsmpModelProperty(description = "产品编码", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "版本编码", required = true)
    private String versionCode;

    @ZsmpModelProperty(description = "目标", required = false)
    private String targetParentEventCode;

    @ZsmpModelProperty(description = "移动端事件列表", required = true)
    private List<MobileAppArtifactVO> appArtifacts;

} 