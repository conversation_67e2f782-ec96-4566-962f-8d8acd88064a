package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @Description: 回滚实例
 * @Author: cher
 * @Date: 2021/7/9 13:37
 **/
@Data
@GatewayModel(description = "回滚实例实体")
public class RollbackInstanceReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实例编码
     */
    @Auth(type = AuthTypeConstant.INSTANCE)
    @GatewayModelProperty(description = "实例编码")
    @NotBlank(message = "实例编码不能为空")
    private String code;

    /**
     * 实例制品编码
     */
    @GatewayModelProperty(description = "实例制品编码", required = false)
    private String artifactCode;

    @GatewayModelProperty( description = "需下线的灰度编码", required = false)
    private String grayscaleCode;

    @ZsmpModelProperty(description = "需下线的小程序版本id", sample = "1")
    private Long versionId;
}
