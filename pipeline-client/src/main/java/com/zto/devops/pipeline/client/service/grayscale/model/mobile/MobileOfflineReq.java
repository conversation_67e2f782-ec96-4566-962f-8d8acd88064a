package com.zto.devops.pipeline.client.service.grayscale.model.mobile;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author：chenguangyu
 * @Description：
 * @Date：2025/7/8 10:03
 */
@Data
@ZsmpModel(description = "移动端(安卓、ios等)下线req")
public class MobileOfflineReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.APPLICATION)
    @ZsmpModelProperty(description = "应用编码", sample = "APP1", required = true)
    private String applicationCode;

    @ZsmpModelProperty(description = "环境", sample = "FAT", required = true)
    private EnvEnum env;

    @ZsmpModelProperty(description = "版本id", sample = "1", required = true)
    private Long versionId;
}
