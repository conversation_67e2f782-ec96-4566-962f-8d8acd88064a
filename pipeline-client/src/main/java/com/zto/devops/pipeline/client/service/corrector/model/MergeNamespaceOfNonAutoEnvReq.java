package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * merge namespace of non autoenv req
 *
 * <AUTHOR>
 * @date 2024-04-28 17:01
 */
@Data
@ZsmpModel(description = "合并常规版本的测试空间,实例迁移到")
public class MergeNamespaceOfNonAutoEnvReq implements Serializable {
    @ZsmpModelProperty(description = "产品code列表", sample = "1", required = true)
    private List<String> productCodeList;
    @ZsmpModelProperty(description = "是否展示要迁移的实例", sample = "1", required = true)
    private Boolean showCurrentInstance;
}
