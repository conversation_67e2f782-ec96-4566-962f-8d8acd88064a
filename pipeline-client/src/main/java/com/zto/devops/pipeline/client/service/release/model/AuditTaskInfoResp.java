package com.zto.devops.pipeline.client.service.release.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/10/23
 */
@Data
@ZsmpModel(description = "OA审批任务详情")
public class AuditTaskInfoResp implements Serializable {

    private static final long serialVersionUID = 1L;
    @ZsmpModelProperty(
            description = "任务唯一标识"
    )
    private String taskId;
    @ZsmpModelProperty(
            description = "业务单号",
            required = true
    )
    private String bizNo;
    @ZsmpModelProperty(
            description = "流程审批编码。对应auditCode",
            required = true
    )
    private String bizType;
    @ZsmpModelProperty(
            description = "任务待办人，assignee+candidateUser 去重后就是任务的全部待办人"
    )
    private String assignee;
    @ZsmpModelProperty(
            description = "任务待办人，assignee+candidateUser 去重后就是任务的全部待办人"
    )
    private List<String> candidateUser;
    @ZsmpModelProperty(
            description = "任务状态,0-审批中；1-已归档；2-已取消",
            required = true
    )
    private Integer status;
    @ZsmpModelProperty(
            description = "流程返回数据",
            hidden = true
    )
    private Map<String, Object> workflowData;
    @ZsmpModelProperty(
            description = "业务参数信息"
    )
    @JSONField(
            jsonDirect = true
    )
    private String variables;
    @ZsmpModelProperty(
            description = "流程节点编码"
    )
    private String nodeCode;
    @ZsmpModelProperty(
            description = "流程节点名称"
    )
    private String nodeName;
    @ZsmpModelProperty(
            description = "流程实例ID"
    )
    private String processInstanceId;
    @ZsmpModelProperty(
            description = "流程定义key值"
    )
    private String processId;
    @ZsmpModelProperty(
            description = "节点业务标识"
    )
    private String bizSign;
    @ZsmpModelProperty(
            description = "工单全部任务列表"
    )
    private String tasksHis;
    @ZsmpModelProperty(
            description = "流程版本号"
    )
    private String versionFlowcenter;
    @ZsmpModelProperty(
            description = "流程仿真标识，true-是流程仿真；false-不是流程仿真"
    )
    private boolean flowTestFlag = false;
    @ZsmpModelProperty(
            description = "上一任务唯一标识"
    )
    private String parentTaskId;
    @ZsmpModelProperty(
            description = "工单发起人"
    )
    private String applicant;
    @ZsmpModelProperty(
            description = "pc端链接"
    )
    private String pcUrl;
    @ZsmpModelProperty(
            description = "移动端链接"
    )
    private String mobileUrl;
    @ZsmpModelProperty(
            description = "业务方pc端链接"
    )
    private String bizPcUrl;
    @ZsmpModelProperty(
            description = "业务方移动端链接"
    )
    private String bizMobileUrl;
}
