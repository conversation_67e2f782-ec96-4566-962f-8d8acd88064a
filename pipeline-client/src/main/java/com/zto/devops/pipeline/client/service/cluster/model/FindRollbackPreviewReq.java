package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FindRollbackPreviewReq implements Serializable {
    private static final long serialVersionUID = 1L;
    @GatewayModelProperty( description = "空间编码", sample = "NS202101010001")
    private String namespaceCode;
    @GatewayModelProperty( description = "快照编码", sample = "NS202101010001")
    private String snapshotCode;
}
