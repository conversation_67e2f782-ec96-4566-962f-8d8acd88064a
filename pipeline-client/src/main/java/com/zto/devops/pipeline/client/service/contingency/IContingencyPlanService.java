package com.zto.devops.pipeline.client.service.contingency;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.contingency.entity.ContingencyExecuteRecordVO;
import com.zto.devops.pipeline.client.model.contingency.entity.ContingencyPlanVO;
import com.zto.devops.pipeline.client.service.contingency.model.*;

public interface IContingencyPlanService {

    /**
     * 新增预案计划
     */
    Result<String> addContingencyPlan(AddContingencyPlanReq req);
    /**
     * 编辑预案计划
     */
    Result<Void> editContingencyPlan(EditContingencyPlanReq req);
    /**
     * 删除预案计划
     */
    Result<Void> deleteContingencyPlan(DeleteContingencyPlanReq req);
    /**
     * 预案计划列表分页查询
     */
    PageResult<ContingencyPlanVO> pageContingencyPlan(PageContingencyPlanReq req);

    /**
     * 预案执行记录列表分页查询
     */
    PageResult<ContingencyExecuteRecordVO> pageContingencyExecuteRecord(PageContingencyExecuteRecordReq req);

    /***
     * 预案执行前校验
     * */
    Result<Void> preCheckExecuteContingencyPlan(PreExecuteContingencyPlanReq req);

}
