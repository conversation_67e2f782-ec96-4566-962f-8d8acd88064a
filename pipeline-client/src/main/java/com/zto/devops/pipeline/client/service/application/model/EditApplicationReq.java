package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.model.zmas.ZmasMobileAppBasicInfo;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;


/**
 * <AUTHOR>
 * @date Created in 2021/7/26
 */

@Data
public class EditApplicationReq implements Serializable {
    @GatewayModelProperty(description = "应用描述")
    private String description;
    @GatewayModelProperty(description = "应用别名")
    private String name;
    @GatewayModelProperty(description = "应用类型编码", required = false)
    private String applicationTypeCode;
    @Auth(type = AuthTypeConstant.APPLICATION)
    @GatewayModelProperty(description = "应用编码", required = false)
    private String code;
    @GatewayModelProperty(description = "基础镜像", required = false)
    private String baseImage;
    @GatewayModelProperty(description = "源码地址",required = false)
    private String gitProjectUrl;
    @GatewayModelProperty(description = "类型参数",required = false)
    private Map<String, Object> typeInput;
    @GatewayModelProperty(description = "apolloAppId",required = false)
    private String apolloAppId;
    @GatewayModelProperty(description = "git源码id",required = false)
    private Long gitProjectId;
    @GatewayModelProperty(description = "第一告警人", required = false)
    private User firstAlerter;
    @GatewayModelProperty(description = "第二告警人", required = false)
    private User secondAlerter;
    @GatewayModelProperty(description = "覆盖率标准值")
    private BigDecimal coverageStandardValue;
    @GatewayModelProperty(description = "是否白名单")
    private Boolean whiteList;
    @GatewayModelProperty(description = "白名单原因", required = false)
    private String whiteListReason;
    @ZsmpModelProperty(description = "预热时间", required = false)
    private Integer warmupTime;

    @ZsmpModelProperty(description = "zmas移动应用基础信息", required = false)
    private ZmasMobileAppBasicInfo zmasMobileAppBasicInfo;
}
