package com.zto.devops.pipeline.client.service.application.model.strategy;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: renxinhui
 * @Description:
 * @Date: 2025/01/02 16:26
 */
@Data
@ZsmpModel(description = "重试应用网络策略Req")
public class RetryAppNetworkStrategyReq implements Serializable {
    @ZsmpModelProperty(description = "应用网络策略编码", sample = "1", required = true)
    private String appNetworkStrategyCode;
}
