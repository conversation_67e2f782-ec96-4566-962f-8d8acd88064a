package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.pipeline.client.model.release.enums.SearchScope;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ListChangeEventsReq implements Serializable {

    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "版本编码")
    private String versionCode;

    @GatewayModelProperty(description = "是否过滤已被应用的", required = false)
    private Boolean filterApplied = false;

    @GatewayModelProperty(description = "搜索关键字", required = false)
    private String key;

    @GatewayModelProperty(description = "搜索关键字", required = false)
    private SearchScope scope = SearchScope.VERSION;

    @GatewayModelProperty(description = "是否过滤不一致的", required = false)
    private Boolean filterDiff = false;
}
