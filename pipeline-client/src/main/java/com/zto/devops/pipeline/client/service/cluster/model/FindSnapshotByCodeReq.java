package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class FindSnapshotByCodeReq implements Serializable {
    private static final long serialVersionUID = 1L;
    @GatewayModelProperty( description = "快照编码", sample = "NS202101010001")
    private String code;
}
