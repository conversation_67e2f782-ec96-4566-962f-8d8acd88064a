package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.pipeline.client.model.engine.vm.ZtoCloudBaseDTO;
import com.zto.devops.pipeline.client.model.engine.vm.ZtoCloudVmImageDTO;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description: 查询中通云主机镜像出参实体
 * @Author: cher
 * @Date: 2022/1/6 13:54
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@GatewayModel(description = "查询中通云主机镜像出参实体")
public class QueryZtoCloudImageResp extends ZtoCloudBaseDTO {

    /**
     * 主机镜像数据列表
     */
    @GatewayModelProperty(description = "主机镜像数据列表")
    private List<ZtoCloudVmImageDTO> image_set;

    /**
     * 总数
     */
    @GatewayModelProperty(description = "总数")
    private Integer total_count;
}
