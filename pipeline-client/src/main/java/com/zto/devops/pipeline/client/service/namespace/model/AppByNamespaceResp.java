package com.zto.devops.pipeline.client.service.namespace.model;

import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.devops.pipeline.client.model.common.model.GeneralVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@ZsmpModel(description = "空间下应用列表出参")
@Getter
@Setter
public class AppByNamespaceResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "产品编码")
    private String productCode;
    @ZsmpModelProperty(description = "环境")
    private EnvEnum env;
    @ZsmpModelProperty(description = "空间编码")
    private String namespaceCode;
    @ZsmpModelProperty(description = "空间名称")
    private String namespaceName;
    @ZsmpModelProperty(description = "集群编码")
    private String clusterCode;
    @ZsmpModelProperty(description = "应用code")
    private String applicationCode;
    @ZsmpModelProperty(description = "应用名称")
    private String name;
    @ZsmpModelProperty(description = "appId")
    private String appId;
    @ZsmpModelProperty(description = "应用类型编码")
    private String applicationTypeCode;
    @ZsmpModelProperty(description = "应用类型")
    private String applicationType;
    @ZsmpModelProperty(description = "部署类型")
    private String deployType;
    @ZsmpModelProperty(description = "实例数")
    private Integer countInstance;
    @ZsmpModelProperty(description = "实例状态")
    private String instanceStatus;
    @ZsmpModelProperty(description = "制品数")
    private Integer countArtifact;
    @ZsmpModelProperty(description = "是否是核心应用")
    private Boolean coreApplication;
    @ZsmpModelProperty(description = "日志地址")
    private String logUrl;
    @ZsmpModelProperty(description = "参数apolloSecret")
    private String apolloSecret;
    @ZsmpModelProperty(description = "部署参数")
    private String deploymentParam;
    @ZsmpModelProperty(description = "默认参数")
    private String defaultDeploymentParam;
    @ZsmpModelProperty(description = "停止命令参数")
    private String stopParam;

    @ZsmpModelProperty(description = "操作")
    private List<GeneralVO> operateList;
}
