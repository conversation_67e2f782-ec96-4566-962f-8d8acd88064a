package com.zto.devops.pipeline.client.service.pinpoint.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: cher
 * @Date: 2023/6/2 13:21
 **/
@Data
public class SyncBugReq implements Serializable {

    private static final long serialVersionUID = 1L;
    @GatewayModelProperty(description = "项目ID")
    private String projectId;
    @GatewayModelProperty(description = "报告ID")
    private String reportId;
    @GatewayModelProperty(description = "应用编码")
    private String appCode ;
    @GatewayModelProperty(description = "任务编码", required = false)
    private String taskCode;
}
