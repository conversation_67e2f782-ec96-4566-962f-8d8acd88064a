package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.pipeline.client.model.release.enums.ReleaseEventType;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AddReleaseEventReq implements Serializable {
    @GatewayModelProperty(description = "上线计划产品编码")
    private String productCode;
    @GatewayModelProperty(description = "上线计划版本编码")
    private String versionCode;
    @GatewayModelProperty(description = "上线计划所属父目录",required = false)
    private String parentCode;
    @GatewayModelProperty(description = "计划内容")
    private Object eventPayload;
    @GatewayModelProperty(description = "计划名称")
    private String name;
    @GatewayModelProperty(description = "变更类型")
    private ReleaseEventType eventType;
}
