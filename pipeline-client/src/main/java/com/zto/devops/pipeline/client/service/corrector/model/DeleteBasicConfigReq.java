package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * delete basic config req
 *
 * <AUTHOR>
 * @date 2024-08-08 19:58
 */
@Data
public class DeleteBasicConfigReq implements Serializable {
    @ZsmpModelProperty(description = "key", sample = "[]", required = true)
    private String key;
}
