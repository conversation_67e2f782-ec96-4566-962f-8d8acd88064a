package com.zto.devops.pipeline.client.service.refresh;

import cn.hutool.http.server.HttpServerRequest;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.zmas.ZmasMigrateAppReq;
import com.zto.devops.pipeline.client.model.zmas.ZmasMigrateVO;
import com.zto.devops.pipeline.client.service.release.model.OssBasicReq;

import java.util.List;

/**
 * @Author: cher
 * @Date: 2023/10/25 14:02
 **/
public interface IMigrateZmasServiceImpl {

    /**
     * 为产品创建zke集群
     *
     * @return
     */
    Result<Integer> refreshZmasApp(List<String> appIdList);

    /**
     * 直接根据zmas对象迁移数据(为避免oss文件下载/读取时失败)
     *
     * @param req 迁移应用信息
     * @return 迁移数据VO列表
     */
    Result<List<ZmasMigrateVO>> migrateZmasAppV2(ZmasMigrateAppReq req);

    /**
     * 导入ZMAS迁移补充字段Excel文件
     *
     * @param zmasFile 上传的csv文件内容
     * @return 迁移数据VO列表
     */
    Result<List<ZmasMigrateVO>> migrateZmasApp(OssBasicReq zmasFile);
}
