package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: cher
 * @Date: 2023/6/8 9:36
 **/
@Data
public class ApplicationCodeReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.APPLICATION)
    @ZsmpModelProperty(description = "应用编码", required = true)
    private String code;

    @ZsmpModelProperty(description = "前缀")
    private String domainPrefix;
}
