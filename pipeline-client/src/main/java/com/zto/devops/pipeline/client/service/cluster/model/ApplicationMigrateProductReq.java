package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2022/1/6
 */
@Data
public class ApplicationMigrateProductReq implements Serializable {

    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty( description = "产品名",required = false)
    private String name;
}
