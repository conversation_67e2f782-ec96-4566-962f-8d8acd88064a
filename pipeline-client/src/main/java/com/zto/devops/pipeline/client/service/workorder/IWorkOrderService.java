package com.zto.devops.pipeline.client.service.workorder;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.rpc.middleware.ApplyAuditDTO;
import com.zto.devops.pipeline.client.model.workorder.vo.WorkOrderVO;
import com.zto.devops.pipeline.client.service.workorder.model.FindWorkOrderReq;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/28
 * @Version 1.0
 */
public interface IWorkOrderService {


    Result<String> simpleAudit(ApplyAuditDTO req);

}
