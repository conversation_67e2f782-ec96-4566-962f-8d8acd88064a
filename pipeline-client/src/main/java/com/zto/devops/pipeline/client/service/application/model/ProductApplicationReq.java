package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 2021/10/11
 */
@Data
public class ProductApplicationReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 应用编码
     */
    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "是否需要mq路由", required = false)
    private Boolean needMqRoute = false;

    @GatewayModelProperty(description = "应用类型集合", required = false)
    private List<String> applicationTypeCodes;

    @GatewayModelProperty(description = "搜索关键字，可以用来模糊搜索appid或者名称", required = false)
    private String keyword;
}
