package com.zto.devops.pipeline.client.service.snapshot;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.cluster.entity.SnapshotVO;
import com.zto.devops.pipeline.client.service.cluster.model.AddSnapshotReq;
import com.zto.devops.pipeline.client.service.snapshot.model.*;

import java.util.List;

/**
 * snapshot service
 *
 * <AUTHOR>
 * @date 2023-03-16 14:14
 **/
public interface ISnapshotService {
    Result<SnapshotResp> findSnapshotByCode(FindSnapshotByCodeReq req);

    PageResult<SnapshotVO> pageSnapshot(PageSnapshotReq req);

    /**
     * 快照预览
     *
     * @param req req
     * @return resp
     */
    Result<List<SnapshotPreviewResp>> findSnapshotPreview(FindSnapshotPreviewReq req);

    /**
     * 添加快照
     *
     * @param req req
     * @return void
     */
    Result<Void> addSnapshot(AddSnapshotReq req);
}