package com.zto.devops.pipeline.client.service.checkItem.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/7/30
 */
@ZsmpModel(description = "更改检查项状态入参")
@Setter
@Getter
public class UpdateCheckItemStatusReq extends CheckItemCodeReq {

    @ZsmpModelProperty(description = "检查项状态", sample = "false", required = true)
    private Boolean status;
}
