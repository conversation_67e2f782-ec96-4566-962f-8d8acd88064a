package com.zto.devops.pipeline.client.service.contingency.model;

import com.zto.devops.pipeline.client.enums.contingency.ContingencyPlanLevelEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Description:
 * @Date: 2025/3/31 16:35
 */

@Data
@ZsmpModel(description = "编辑预案req")
public class EditContingencyPlanReq implements Serializable {

    @ZsmpModelProperty(description = "预案编码", required = true)
    private String contingencyPlanCode;
    @ZsmpModelProperty(description = "预案名称", required = true)
    private String name;
    @ZsmpModelProperty(description = "预案描述", required = false)
    private String description;
    @ZsmpModelProperty(description = "业务影响", required = true)
    private ContingencyPlanLevelEnum contingencyPlanLevel;
}