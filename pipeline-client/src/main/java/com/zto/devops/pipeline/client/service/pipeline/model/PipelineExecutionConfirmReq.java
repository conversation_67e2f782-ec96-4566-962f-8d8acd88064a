package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.ConfirmResultEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: chenguangyu
 * @Description:
 * @Date: 2023/7/7 10:49
 */
@ZsmpModel(description = "确认流水线执行-入参实体")
@Data
public class PipelineExecutionConfirmReq implements Serializable {

    @Auth(type = AuthTypeConstant.PIPELINE_CONFIRM_DEPLOY)
    @ZsmpModelProperty(description = "执行节点的编码", sample = "1", required = true)
    private String executionNodeCode;

    @ZsmpModelProperty(description = "确认结果", sample = "1", required = true)
    private ConfirmResultEnum result;

}
