package com.zto.devops.pipeline.client.service.application.model;

import com.zto.zsmp.annotation.gateway.GateWayAliasFor;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2022/1/25
 */
@Data
public class UserCookieReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @GateWayAliasFor(value = "$req.http.heads.cookie")
    private String cookie;
}
