package com.zto.devops.pipeline.client.service.pipeline;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.flow.command.batch.RetryBatchCommand;
import com.zto.devops.pipeline.client.service.pipeline.model.PipelineExecutionAbortReq;
import com.zto.devops.pipeline.client.service.pipeline.model.PipelineExecutionConfirmReq;
import com.zto.devops.pipeline.client.service.pipeline.model.PipelineExecutionContinueReq;
import com.zto.devops.pipeline.client.service.pipeline.model.PipelineExecutionRetryReq;

/**
 * @Author: chenguangyu
 * @Description:
 * @Date: 2023/7/7 10:44
 */
public interface IPipelineExecutionService {

    /**
     * 中断流水线实例
     */
    Result<Void> executionAbort(PipelineExecutionAbortReq req);

    /**
     * 确认流水线执行
     */
    Result<Void> confirmExecution(PipelineExecutionConfirmReq req);

    /**
     * 重试流水线实例
     */
    Result<Void> retryExecution(PipelineExecutionRetryReq req);

    /**
     * 继续流水线实例
     */
    Result<Void> continueExecution(PipelineExecutionContinueReq req);

    /**
     * 上线计划重试发布批次
     *
     * @param command
     */
    void retryBatch(RetryBatchCommand command);
}
