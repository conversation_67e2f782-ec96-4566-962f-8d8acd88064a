package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.enums.PlanStatusEnum;
import com.zto.devops.pipeline.client.enums.pipeline.PipelineExecutionTriggerTypeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: chenguangyu
 * @Description:
 * @Date: 2023/6/28 13:40
 */
@ZsmpModel(description = "流水线-运行历史入参实体")
@Data
public class PipelineExecutionReq extends PageQueryBase implements Serializable {

    @ZsmpModelProperty(description = "产品编码" , required = true)
    private String productCode;

    @ZsmpModelProperty(description = "任务目标编码", required = false)
    private String targetCode;

    @ZsmpModelProperty(description = "目标名称", sample = "1", required = false)
    private String targetName;

    @ZsmpModelProperty(description = "状态", required = false)
    private List<PlanStatusEnum> statusList;

    @ZsmpModelProperty(description = "空间编码", required = false)
    private List<String> namespaceCodeList;

    @ZsmpModelProperty(description = "运行人id", required = false)
    private List<Long> userIdList;

    @ZsmpModelProperty(description = "开始时间", required = false)
    private Date startDate;
    @ZsmpModelProperty(description = "结束时间", required = false)
    private Date endDate;

    @ZsmpModelProperty(description = "流水线配置编码", required = false)
    private List<String> executionConfigCodeList;

    @ZsmpModelProperty(description = "触发方式", required = false)
    private List<PipelineExecutionTriggerTypeEnum> triggerList;

    @ZsmpModelProperty(description = "应用code", required = false)
    private List<String> applicationCodes;

    @ZsmpModelProperty(description = "版本编码", required = false)
    private String versionCode;

}
