package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.FlowEventEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CancelReleaseReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "版本编码", required = true)
    private String versionCode;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @ZsmpModelProperty(description = "应用code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "版本行为")
    private FlowEventEnum flowEvent;
}
