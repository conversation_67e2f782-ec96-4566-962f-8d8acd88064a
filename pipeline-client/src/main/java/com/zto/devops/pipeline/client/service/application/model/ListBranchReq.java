package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2022/9/13
 * @Version 1.0
 */
@Data
public class ListBranchReq extends PageQueryBase implements Serializable {

    @Auth(type = AuthTypeConstant.APPLICATION)
    private String code;

    private String searchKey;
}
