package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.model.application.entity.ApplicationMigrateVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 2022/1/5
 */
@Data
public class ApplicationMigrateReq implements Serializable {
    @GatewayModelProperty( description = "空间-》新空间关系列表")
    private List<ApplicationMigrateVO> applicationMigrateVOS;

    @GatewayModelProperty( description = "应用编码")
    private List<String> applicationCodes;

    @GatewayModelProperty( description = "新产品编码")
    private String newProductCode;

    @GatewayModelProperty( description = "新产品名字")
    private String newProductName;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty( description = "原产品编码")
    private String oldProductCode;
}
