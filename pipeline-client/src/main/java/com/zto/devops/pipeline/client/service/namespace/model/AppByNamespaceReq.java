package com.zto.devops.pipeline.client.service.namespace.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@ZsmpModel(description = "空间下应用列表入参")
@Getter
@Setter
public class AppByNamespaceReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.NAMESPACE)
    @ZsmpModelProperty( description = "空间编码", required = true)
    private String namespaceCode;

    @ZsmpModelProperty(description = "应用类型id")
    private List<String> applicationTypeCode;

    @ZsmpModelProperty(description = "应用编码")
    private String appCode;
}
