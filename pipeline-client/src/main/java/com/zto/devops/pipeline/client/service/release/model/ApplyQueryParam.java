package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.pipeline.client.model.release.enums.WorkOrderStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

@Data
public class ApplyQueryParam extends ApplyBasePathParam implements Serializable {

    private String name;

    private List<String> applyType;

    private List<WorkOrderStatusEnum> status;

    private String frontendStatus;

    private String childApplyType;

    private String createUser;

    private boolean createdByMe;

    private String clusterType;

    private String productCode;

    private String env;

    private String appId;

    private Integer tabType;

    private Collection<Long> applyIds;

    private Integer page;

    private Integer pageSize;

    //工单类型使用
    private String handleType;
}
