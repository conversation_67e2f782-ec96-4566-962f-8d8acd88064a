package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * mirror info resp
 *
 * <AUTHOR>
 * @date 2023-04-06 18:19
 */
@Data
@ZsmpModel(description = "获取镜像地址信息出参")
public class MirrorInfoResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "版本名称")
    private String flowName;

    @ZsmpModelProperty(description = "镜像地址")
    private String mirrorUrl;

    @ZsmpModelProperty(description = "制品备注")
    private String remark;

    @ZsmpModelProperty(description = "制品commit")
    private String commit;
}