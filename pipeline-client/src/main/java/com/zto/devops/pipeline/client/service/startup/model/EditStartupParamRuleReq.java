package com.zto.devops.pipeline.client.service.startup.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * startup param edit req
 *
 * <AUTHOR>
 * @date 2025-04-10 17:32
 */
@Data
public class EditStartupParamRuleReq extends AddStartupParamRuleReq implements Serializable {

    /**
     * 启动参数配置编码
     */
    @GatewayModelProperty(description = "启动参数配置编码", sample = "1")
    private String startupParamConfigCode;

    @GatewayModelProperty(description = "状态", sample = "1")
    private Boolean status;

    @GatewayModelProperty( description = "名称", sample = "兔喜专用")
    private String name;

    @GatewayModelProperty( description = "优先级", sample = "1")
    private Integer priority;

    @GatewayModelProperty( description = "模板(即启动参数)", sample = "-Da=b")
    private String deploymentParam;


}
