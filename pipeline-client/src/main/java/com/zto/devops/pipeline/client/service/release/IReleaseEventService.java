package com.zto.devops.pipeline.client.service.release;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.pipeline.client.model.flow.entity.FlowRelatedAppAndInstanceVO;
import com.zto.devops.pipeline.client.model.release.vo.CheckApolloConfigVO;
import com.zto.devops.pipeline.client.model.release.vo.ReleaseEventVO;
import com.zto.devops.pipeline.client.model.release.vo.ReleaseGroupedEventVO;
import com.zto.devops.pipeline.client.service.release.model.*;
import com.zto.devops.pipeline.client.service.req.FlowCodeReq;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/29
 * @Version 1.0
 */
public interface IReleaseEventService {

    Result<ReleaseEventVO> addReleaseEvent(AddReleaseEventReq req);

    Result<List<ReleaseEventVO>> addReleaseEvents(AddReleaseEventReq req);

    Result<Void> editReleaseEvent(EditReleaseEventReq req);

    Result<Void> deleteReleaseEvent(DeleteReleaseEventReq req);

    Result<Void> applyEvent(ApplyEventReq req);

    Result<Void> cancelApplyEvents(CancelApplyReq req);

    /**
     * 移动变更项
     * @return
     */
    Result<Void> moveEvents(MoveEventReq req);

    Result<ListTargetEventGroupResp> listTargetEventGroups(ListMoveTargetReq req);

    Result<List<ReleaseEventVO>> listReleaseEvents(ListReleaseEventReq req);

    Result<List<ReleaseGroupedEventVO>> listEventsForApply(ListAllEventsReq req);

    Result<Void> sortReleaseEvent(SortReleaseEventReq req);

    Result<String> generatePreSignedUrl(OssBasicReq req);

    Result<String> getFileUrl(OssBasicReq req);

    Result<Void> coverReleaseEvent(CoverReleaseEventReq req);

    Result<Void> generateRollback(GenerateRollbackReq req);

    Result<Void> updateRollback(UpdateRollbackReq req);

    Result<Boolean> checkDiffTip(ListChangeEventsReq req);

    /**
     * 获取预发需要拆分批次的数据
     *
     * @param req
     * @return
     */
    Result<List<FlowRelatedAppAndInstanceVO>> listRelatedAppAndPreInstances(FlowCodeReq req);

    /**
     * 保存发布批次
     *
     * @param req
     * @return
     */
    Result<Void> saveDeployStrategy(SaveDeployStrategyReq req);

    /**
     * 保存批次信息
     * @param saveDeployBatchReq
     * @return
     */
    Result<Void> saveDeployBatch(SaveDeployBatchReq saveDeployBatchReq);

    /**
     * 移动批次信息
     * @param saveDeployBatchReq
     * @return
     */
    Result<Void> moveDeployBatch(MoveDeployBatchReq saveDeployBatchReq);

    /**
     * 移除发布批次
     * @param req
     * @return
     */
    Result<Void> removeDeployBatch(CancelApplyReq req);

    CheckApolloConfigVO checkApolloConfig(CheckApolloConfigReq req); //中间件

    /**
     * 批量保存移动端发布事件
     */
    Result<Void> saveMobileDeployEventBatch(SaveMobileDeployEventBatchReq req);

    /**
     * 全局搜索变更事件
     * 
     * @param request 搜索请求参数
     * @return 搜索结果
     */
    PageResult<ReleaseEventVO> searchReleaseEvents(SearchReleaseEventReq request);

    /**
     * 列出所有可选择的事件类型
     * 
     * @param req 请求参数
     * @return 可选择的事件类型列表
     */
    Result<List<ReleaseEventTypeInfo>> listSelectableEventTypes(ListSelectableEventTypesReq req);
}
