package com.zto.devops.pipeline.client.service.refresh;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.pipeline.entity.PipelineConfigExtendNodeVO;
import com.zto.devops.pipeline.client.service.refresh.model.ProductOpenPushBuildReq;

import java.util.List;

/**
 * @Author: cher
 * @Date: 2023/10/25 14:02
 **/
public interface IRefreshPipelineService {

    /**
     * 为每个产品创建一条默认流水线
     *
     * @return
     */
    Result<Void> productCreateDefaultPipeline();

    /**
     * 原简易流水线的版本刷成产品的默认流水线
     *
     * @return
     */
    Result<Void> productEasyToDefaultPipeline();

    /**
     * 原简易流水线的流水线运行刷成产品的默认流水线
     *
     * @return
     */
    Result<Void> refreshPipelineExecutionEasyToDefaultPipeline();

    /**
     * 在流水线配置部署卡片加上是否提交构建部署按钮，且简易流水线默认都刷成关闭 ， 其他流水线都刷成打开
     *
     * @return
     */
    Result<Integer> pushBuildAutoDeployWithDeployConfig();

    /**
     * 产品开启提交即构建
     *
     * @param req
     * @return
     */
    Result<Void> productOpenPushBuild(ProductOpenPushBuildReq req);

    /**
     * 配置节点属性重构
     *
     * @param pipelineConfigCodeList
     * @return
     */
    Result<Integer> refreshPipelineConfigSwitchAndParameter(List<String> pipelineConfigCodeList);

    /**
     * 刷新代码扫描流水线配置节点属性
     *
     * @param pipelineConfigCode
     * @return
     */
    Result<Integer> refreshIncrementBugScanPipelineConfigNodeParameter(String pipelineConfigCode);

    /**
     * 重构流水线配置节点(流水线2.0)
     *
     * @param pipelineConfigCodeList
     * @return
     */
    Result<Integer> resetPipelineConfigNodeForPipelineV2_0(List<String> pipelineConfigCodeList);

    /**
     * 重构流水线配置节点(流水线3.0)
     *
     * @param pipelineConfigCodeList
     * @return
     */
    Result<Integer> resetPipelineConfigNodeForPipelineV3_0(List<String> pipelineConfigCodeList);

    /**
     * 操作流水线配置节点,包括新增、更新、删除
     *
     * @param nodeVO
     * @return
     */
    Result<Integer> operatePipelineConfigNode(PipelineConfigExtendNodeVO nodeVO);

    /**
     * 刷新未部署oss实例的状态为进行中，制品为相同集群下最新部署实例的制品
     *
     * @return
     */
    Result<Void> ossInstanceStatusAndArtifact();

    /**
     * 代码扫描、自动化冒烟测试-新增插件开关-“追加评论到MR中”，默认打开
     *
     * @return
     */
    Result<Integer> refreshNodeParameterCodeAddCommentToMr(String code);

    Result<Integer> refreshPipelineConfigNodeAndLink_V1(List<String> pipelineConfigCodeList);

    /**
     * 迁移制品到新的oss
     *
     * @return
     */
    Result<Void> artifactMigration(int threadNum);

    /**
     *刷新预发空间
     * 空间tag【空间表tag字段】默认pre，存量预发空间实例数据刷下
     * 存量产品如果没有预发布，空间下的集群也需要创建
     * */
    Result<Integer> refreshPreNamespace(List<String> productCodes);

    /**
     * 刷新实例表中appId和version信息
     *
     * @return
     */
    Result<Void> refreshInstanceAppIdAndVersion();

    /**
     * 刷新应用到宙斯系统
     *
     * @return
     */
    Result<Void> refreshApplicationToEtl(List<String> appCodeList);

    /**

     * 刷新添加合并后的小版本的release分支

     * @return

     */

    Result<Void> mergedMinorFlowAddReleaseBranch();
}
