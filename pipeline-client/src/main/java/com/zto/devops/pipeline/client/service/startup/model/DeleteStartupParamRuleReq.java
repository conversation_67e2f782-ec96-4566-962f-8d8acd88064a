package com.zto.devops.pipeline.client.service.startup.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * startup param delete req
 *
 * <AUTHOR>
 * @date 2025-04-10 17:54
 */
@Data
public class DeleteStartupParamRuleReq implements Serializable {
    /**
     * 启动参数配置编码
     */
    @GatewayModelProperty(description = "启动参数配置编码", sample = "1")
    private String startupParamConfigCode;
}
