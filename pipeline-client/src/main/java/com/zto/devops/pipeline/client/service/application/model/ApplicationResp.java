package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.pipeline.client.enums.DeployTypeEnum;
import com.zto.devops.pipeline.client.model.common.model.GeneralVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date Created in 2021/7/26
 */
@Data
public class ApplicationResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 所属产品id
     */
    @GatewayModelProperty(description = "产品编码", required = false)
    private String productCode;


    @GatewayModelProperty(description = "应用编码", required = false)
    private String code;

    /**
     * 应用别名
     */
    @GatewayModelProperty(description = "应用别名", required = false)
    private String name;

    /**
     * APPID
     */
    @GatewayModelProperty(description = "APPID", required = false)
    private String appId;


    /**
     * 应用类型id
     */
    @GatewayModelProperty(description = "应用类型", required = false)
    private String applicationTypeCode;

    /**
     * 应用类型名字
     */
    @GatewayModelProperty(description = "应用类型名字", required = false)
    private String applicationTypeName;

    /**
     * 部署类型
     */
    @GatewayModelProperty(description = "部署类型", required = false)
    private DeployTypeEnum deployType;

    /**
     * 创建时间
     */
    @GatewayModelProperty(description = "创建时间", required = false)
    private String gmtCreate;

    /**
     * 创建人id
     */
    @GatewayModelProperty(description = "创建人ID")
    private Long creatorId;

    @GatewayModelProperty(description = "git源码Id")
    private Long gitProjectId;

    /**
     * 创建人
     */
    @GatewayModelProperty(description = "创建人")
    private String creator;

    /**
     * 第一负责人id
     */
    @GatewayModelProperty(description = "第一负责人id")
    private Long firstAlerterId;

    /**
     * 第一负责人名称
     */
    @GatewayModelProperty(description = "第一负责人名称")
    private String firstAlerterName;

    /**
     * 第二负责人id
     */
    @GatewayModelProperty(description = "第二负责人id")
    private Long secondAlerterId;

    /**
     * 第二负责人名称
     */
    @GatewayModelProperty(description = "第二负责人名称")
    private String secondAlerterName;

    @GatewayModelProperty(description = "覆盖率标准值")
    private BigDecimal coverageStandardValue;
    @GatewayModelProperty(description = "是否白名单")
    private Boolean whiteList;
    @GatewayModelProperty(description = "白名单原因", required = false)
    private String whiteListReason;

    /**
     * 创建时间
     */
    @GatewayModelProperty(description = "更新时间", required = false)
    private String gmtModified;

    @GatewayModelProperty(description = "按钮")
    private List<GeneralVO> backButtonList;

    @GatewayModelProperty(description = "是否是核心应用", required = false)
    private Boolean coreApplication;

    @GatewayModelProperty(description = "产品名称", required = false)
    private String productName;

    @GatewayModelProperty(description = "核心应用操作权限", required = false)
    private Boolean coreApplicationSwitchPermission;

    @GatewayModelProperty(description = "产品负责人", required = false)
    private String productUserName;

    @GatewayModelProperty(description = "开发负责人", required = false)
    private String devopsUserName;

    @GatewayModelProperty(description = "测试负责人", required = false)
    private String testUserName;

    @GatewayModelProperty(description = "项目经理负责人", required = false)
    private String projectUserName;
}
