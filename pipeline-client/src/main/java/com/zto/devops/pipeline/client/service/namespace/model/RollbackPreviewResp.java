package com.zto.devops.pipeline.client.service.namespace.model;

import com.zto.devops.pipeline.client.model.cluster.entity.ApplicationRollbackLineVO;
import com.zto.devops.pipeline.client.model.cluster.entity.RollbackLineVO;
import com.zto.devops.pipeline.client.model.grayscale.entity.GrayscaleSimpleVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * rollback preview resp
 *
 * <AUTHOR>
 * @date 2023-03-17 15:29
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RollbackPreviewResp implements Serializable {
    private static final long serialVersionUID = 1L;

//    @GatewayModelProperty( description = "应用回滚明细", sample = "[]")
//    private List<ApplicationRollbackLineVO> applicationList;

    @GatewayModelProperty( description = "回滚明细", sample = "[]")
    private List<RollbackLineVO> rollbackLines;

    @GatewayModelProperty( description = "灰度列表", sample = "[]")
    private List<GrayscaleSimpleVO> grayscales;
//    @GatewayModelProperty( description = "可选制品列表", sample = "[]")
//    private List<ArtifactDetailVO> artifactList;


    public RollbackPreviewResp(List<RollbackLineVO> rollbackLines) {
        this.rollbackLines = rollbackLines;
    }
}
