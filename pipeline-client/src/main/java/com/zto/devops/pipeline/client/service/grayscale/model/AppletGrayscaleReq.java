package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.devops.pipeline.client.enums.GrayscaleTypeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ZsmpModel(description = "小程序灰度req")
public class AppletGrayscaleReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.APPLICATION)
    @ZsmpModelProperty(description = "应用编码", sample = "APP1", required = true)
    private String applicationCode;

    @ZsmpModelProperty(description = "环境", sample = "FAT", required = true)
    private EnvEnum env;

    @ZsmpModelProperty(description = "版本id", sample = "1", required = true)
    private Long versionId;

    @ZsmpModelProperty(description = "描述", sample = "1", required = false)
    private String grayscaleDescribe;

    @ZsmpModelProperty(description = "发布类型:灰度、全网", sample = "1", required = true)
    private GrayscaleTypeEnum type;

    @ZsmpModelProperty(description = "百分比", sample = "1", required = false)
    private BigDecimal ratio;

    @ZsmpModelProperty(description = "灰度规则", sample = "1", required = false)
    private String rules;

    @ZsmpModelProperty(description = "前端展示数据", sample = "1", required = false)
    private String uiData;
}
