package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.pipeline.client.model.release.enums.MessageLevelEnum;
import com.zto.devops.pipeline.client.model.release.enums.ReleaseEventType;
import com.zto.devops.pipeline.client.utils.NumberChineseUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * 部署批次标签信息
 */
@Data
public class ReleasePlanDeployBatchTagVO implements Serializable {
    /**
     * 批次描述
     */
    private String batchDesc;

    /**
     * 批次序号
     */
    private Integer deployBatch;

    /**
     * 批次编码(上线计划eventCode)
     */
    private String batchCode;

    /**
     * 事件类型
     */
    private ReleaseEventType eventType;


    /**
     * 用来显示提示信息
     */
    private String message;

    /**
     * 提示信息显示的等级
     */
    private MessageLevelEnum level;


    public void buildMessage(boolean proRoomAvailable,
                             boolean backupRoomAvailable) {
        if (eventType == null) {
            return;
        }
        String roomName = "";
        if (eventType.proDeployBatchEvent() && !proRoomAvailable) {
            roomName = "生产机房";
        }
        if (eventType.backupDeployBatchEvent() && !backupRoomAvailable) {
            roomName = "灾备机房";
        }
        if (StringUtil.isEmpty(roomName)) {
            return;
        }
        message = String.format("当前【%s】不可用, 无需进行发布", roomName);
        level = MessageLevelEnum.NO_PASS;
    }

    public String getBatchDesc() {
        if (this.deployBatch == null || StringUtil.isNotEmpty(this.batchDesc)) {
            return this.batchDesc;
        }
        String prefix = "";
        if (ReleaseEventType.DEPLOY_BATCH.equals(eventType)) {
            prefix = "生产发布-";
        }
        if (ReleaseEventType.DEPLOY_PRE_BATCH.equals(eventType)) {
            prefix = "预发部署-";
        }
        if (ReleaseEventType.DEPLOY_BACKUP_BATCH.equals(eventType)) {
            prefix = "灾备发布-";
        }
        if (StringUtil.isEmpty(prefix)) {
            throw new ServiceException("不支持的事件类型:" + eventType.getEventName());
        }
        String number = NumberChineseUtil.format(deployBatch);
        return String.format("%s批次%s",prefix,number);
    }
}
