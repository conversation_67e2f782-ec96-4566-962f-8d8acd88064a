package com.zto.devops.pipeline.client.service.startup.model;

import com.zto.devops.pipeline.client.model.startupparam.vo.StartupParamConfigVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * startup param resp
 *
 * <AUTHOR>
 * @date 2025-04-10 15:51
 */
@Data
public class QueryStartupParamRuleResp implements Serializable {
    @GatewayModelProperty(description = "启动参数规则列表", sample = "[]", required = false)
    private List<StartupParamConfigVO> configList;
}
