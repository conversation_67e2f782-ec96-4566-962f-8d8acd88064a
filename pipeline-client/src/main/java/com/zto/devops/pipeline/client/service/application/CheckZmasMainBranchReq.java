package com.zto.devops.pipeline.client.service.application;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
/**
 * CheckZmasMainBranchReq
 *
 * <AUTHOR>
 * @date 2025-06-27 14:20
 */
@Data
public class CheckZmasMainBranchReq implements Serializable {
    @GatewayModelProperty(description = "gitUrl", sample = "https://github.com/zto-smart/zmas.git")
    private String gitUrl;

    @GatewayModelProperty(description = "branch", sample = "main")
    private String mainBranch;
}
