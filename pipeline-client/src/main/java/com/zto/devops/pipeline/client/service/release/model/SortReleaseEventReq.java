package com.zto.devops.pipeline.client.service.release.model;


import com.zto.doc.annotation.ZModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SortReleaseEventReq implements Serializable {

    @ZModelProperty(description = "操作排序的对象", required = true)
    private String idCode;

    @ZModelProperty(description = "版本编码", required = true)
    private String versionCode;

    @ZModelProperty(description = "要插入位置的前一个idCode")
    private String beforeIdCode;

    @ZModelProperty(description = "要插入位置的后一个idCode")
    private String afterIdCode;

    @ZModelProperty(description = "父code, 仅当beforeId与after为空时才使用")
    private String parentIdCode;

    @ZModelProperty(description = "当前排序编号")
    private String sortTitle;

    @ZModelProperty(description = "当前排序名称")
    private String name;
}
