package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.devops.pipeline.client.enums.FlowBusinessTypeEnum;
import com.zto.devops.pipeline.client.model.common.model.GeneralVO;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 移动端构建状态VO类，用于控制构建过程中的提示信息和按钮状态
 */
@Data
public class MobileBuildButtonsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 流程编码
     */
    @GatewayModelProperty(description = "流程编码")
    private String flowCode;

    /**
     * 流程类型
     */
    @GatewayModelProperty(description = "流程类型")
    private FlowBusinessTypeEnum flowBusinessType;

    /**
     * 构建是否通过标识
     */
    @ZsmpModelProperty(description = "构建是否通过标识", sample = "true", required = false)
    private boolean buildAllowed;

    /**
     * 提示信息内容
     */
    @ZsmpModelProperty(description = "提示信息内容", sample = "当前应用存在待合并代码", required = false)
    private String promptMessage;

    /**
     * 操作按钮信息
     */
    @ZsmpModelProperty(description = "操作按钮信息", sample = "合并代码", required = false)
    private List<GeneralVO> actionButtons;

    /**
     * 构建按钮列表（构建测试包、构建生产包等）
     */
    @ZsmpModelProperty(description = "构建按钮列表", sample = "[{\"code\":\"buildTest\",\"name\":\"构建测试包\"}]", required = false)
    private List<GeneralVO> buildButtons;
}