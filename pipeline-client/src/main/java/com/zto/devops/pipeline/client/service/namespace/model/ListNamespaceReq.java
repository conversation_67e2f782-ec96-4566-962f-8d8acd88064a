package com.zto.devops.pipeline.client.service.namespace.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.BranchEnum;
import com.zto.devops.pipeline.client.enums.FlowBusinessTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * list namespace req
 *
 * <AUTHOR>
 * @date 2023-03-16 09:50
 */
@Getter
@Setter
public class ListNamespaceReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 流程类型
     */
    @GatewayModelProperty(description = "流程类型")
    @NotNull(message = "流程类型不能为空")
    private FlowBusinessTypeEnum flowBusinessType;

    /**
     * 流程编码
     */
    @Auth(type = AuthTypeConstant.FLOW)
    @GatewayModelProperty(description = "流程编码")
    @NotBlank(message = "流程编码不能为空")
    private String flowCode;

    @GatewayModelProperty(description = "名称", required = false)
    private String name;

    @GatewayModelProperty(description = "部署分支编码", required = false)
    private BranchEnum branch;
}
