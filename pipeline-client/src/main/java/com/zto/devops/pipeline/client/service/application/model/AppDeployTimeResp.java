package com.zto.devops.pipeline.client.service.application.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * app deploy time resp
 *
 * <AUTHOR>
 * @date 2023-04-07 10:42
 */
@Data
public class AppDeployTimeResp implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 部署环境
     * */
    private String env;
    /**
     * appId
     * */
    private String appId;

    /**
     * 最近一次部署时间
     * */
    private Date lastDeploymentTime;

    public AppDeployTimeResp() {
    }

    public AppDeployTimeResp(String env, String appId, Date lastDeploymentTime) {
        this.env = env;
        this.appId = appId;
        this.lastDeploymentTime = lastDeploymentTime;
    }
}
