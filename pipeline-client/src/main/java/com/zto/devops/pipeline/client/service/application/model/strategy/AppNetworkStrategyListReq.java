package com.zto.devops.pipeline.client.service.application.model.strategy;

import com.zto.devops.pipeline.client.enums.application.DirectionTypeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: ch<PERSON><PERSON>yu
 * @Description:
 * @Date: 2024/7/11 11:26
 */
@Data
@ZsmpModel(description = "应用网络策略列表查询Req")
public class AppNetworkStrategyListReq implements Serializable {
    @ZsmpModelProperty(description = "应用编码", sample = "1", required = true)
    private String applicationCode;

    @ZsmpModelProperty(description = "环境",required = false)
    private List<String> envs;

    @ZsmpModelProperty(description = "网络策略类型。EGRESS(出口(Egress))", sample = "EGRESS", required = false)
    private List<DirectionTypeEnum> directionTypeList;

    /**
     * 关键字：模糊匹配域名和ip
     */
    @ZsmpModelProperty(description = "关键字",required = false)
    private String keywords;
}
