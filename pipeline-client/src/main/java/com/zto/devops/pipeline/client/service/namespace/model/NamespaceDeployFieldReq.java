package com.zto.devops.pipeline.client.service.namespace.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * namespace  deploy field query req
 *
 * <AUTHOR>
 * @date 2023-03-16 10:36
 */
@Getter
@Setter
public class NamespaceDeployFieldReq implements Serializable {
    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;
    @GatewayModelProperty(description = "环境")
    private EnvEnum env;
    @GatewayModelProperty(description = "空间编码", required = false)
    private String nameSpaceCode;
}
