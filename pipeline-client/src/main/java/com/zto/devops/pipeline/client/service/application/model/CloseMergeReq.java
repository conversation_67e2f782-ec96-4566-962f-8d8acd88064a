package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2021/10/13
 */
@Data
public class CloseMergeReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.GIT_MERGE_LINE_ID)
    @GatewayModelProperty(description = "mr记录Id")
    private Long id;
//    @GatewayModelProperty(description = "所属版本Code")
//    private String flowCode;
//    @GatewayModelProperty(description = "所属产品Code")
//    private String productCode;
}
