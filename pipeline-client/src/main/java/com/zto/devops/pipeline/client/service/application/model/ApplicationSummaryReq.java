package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ApplicationSummaryReq extends PageQueryBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "应用id",required = false)
    private String appId;

    @GatewayModelProperty(description = "应用类型id",required = false)
    private List<String> applicationTypeCodes;

    @GatewayModelProperty(description = "产品codeList",required = false)
    private List<String> productCodeList;

    @GatewayModelProperty(description = "产品负责人idList",required = false)
    private List<Long> productUserIdList;

    @GatewayModelProperty(description = "开发负责人idList",required = false)
    private List<Long> developUserIdList;

    @GatewayModelProperty(description = "第一负责人idList",required = false)
    private List<Long> firstAlerterIdList;

    @GatewayModelProperty(description = "第二负责人idList",required = false)
    private List<Long> secondAlerterIdList;

    @GatewayModelProperty(description = "测试负责人idList",required = false)
    private List<Long> testUserIdList;

    @GatewayModelProperty(description = "项目经理负责人idList",required = false)
    private List<Long> projectUserIdList;

    @GatewayModelProperty(description = "是否核心应用",required = false)
    private Boolean coreApplication;
}
