package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.pipeline.client.enums.FlowEventEnum;
import com.zto.devops.pipeline.client.model.release.vo.apply.UndoPreCheckResult;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/21
 */
@Data
public class CancelReleasePreCheckResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 能否操作
     */
    private boolean canOperate;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 检查列表
     */
    private List<UndoPreCheckResult> list;

    public boolean isCanOperate() {
        return CollectionUtil.isEmpty(list) || list.stream().allMatch(UndoPreCheckResult::isCanOperate);
    }

    public void fillMessage(FlowEventEnum flowEventEnum) {
        String targetStatus = "回归通过";
        if (flowEventEnum == FlowEventEnum.BACK_WITH_PLAN) {
            targetStatus = "开发中";
        }
        setMessage(String.format("该操作会将上线计划的状态退到【只读状态】；版本的状态会退到【%s】；相关工单会触发对应的操作，请确认是否发起该操作？",targetStatus));
    }

    public String getMessage() {
        if (isCanOperate()) {
            return message;
        } else {
            return "存在正在进行中的工单，无法发起该操作";
        }
    }
}
