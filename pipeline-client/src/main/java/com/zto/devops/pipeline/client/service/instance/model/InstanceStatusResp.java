package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/10/19
 * @Version 1.0
 */
@Data
@GatewayModel(description = "实例")
public class InstanceStatusResp implements Serializable {

    private static final long serialVersionUID = 1L;

    private String value;
    private String key;
}
