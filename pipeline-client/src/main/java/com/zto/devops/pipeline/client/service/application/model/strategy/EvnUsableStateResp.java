package com.zto.devops.pipeline.client.service.application.model.strategy;

import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2024/1/13
 */
@Data
public class EvnUsableStateResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "环境", required = false)
    private EnvEnum env;

    @ZsmpModelProperty(description = "是否可用", required = false)
    private Boolean isUsable;

    @ZsmpModelProperty(description = "不可用原因", required = false)
    private String reason;
}
