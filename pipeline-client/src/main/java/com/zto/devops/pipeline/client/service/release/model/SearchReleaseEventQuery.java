package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.StringUtil;
import com.zto.devops.pipeline.client.model.release.enums.ReleaseEventType;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 变更事件搜索查询聚合根
 * 
 * <AUTHOR>
 * @Description 封装变更事件搜索的所有条件
 * @Date 2025-01-27
 * @Version 1.0
 */
@Data
public class SearchReleaseEventQuery {

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 变更类型列表
     */
    private List<ReleaseEventType> eventTypes;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 变更人ID列表
     */
    private List<Long> transactor;

    /**
     * 版本编码列表
     */
    private List<String> versionCodes;

    /**
     * 是否已应用
     */
    private Boolean applied;

    /**
     * 是否已上线
     */
    private Boolean released;

    /**
     * 搜索关键词
     */
    private String keyword;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 20;

    /**
     * 当前版本编码（不用于SQL查询）
     */
    private String currentVersionCode;

    /**
     * 验证查询条件
     */
    public void validate() {
        if (StringUtil.isBlank(productCode)) {
            throw new ServiceException("产品编码不能为空");
        }
        
        if (pageNum != null && pageNum < 1) {
            throw new ServiceException("页码必须大于0");
        }
        if (pageSize != null && pageSize > 200) {
            throw new ServiceException("每页大小必须在1~200之间");
        }
    }
} 