package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2022/2/9
 */
@Data
public class AppByGitIdReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "源码id")
    private Long gitProjectId;

    @Auth(type = AuthTypeConstant.APPLICATION)
    @GatewayModelProperty(description = "应用编码")
    private String code;
}
