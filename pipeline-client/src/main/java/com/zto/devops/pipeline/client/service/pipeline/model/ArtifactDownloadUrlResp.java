package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * artifact download url resp
 *
 * <AUTHOR>
 * @date 2023-04-07 09:23
 */
@Data
public class ArtifactDownloadUrlResp implements Serializable {

    private static final long serialVersionUID = 5525694890621214074L;

    @GatewayModelProperty(description = "下载地址")
    private String downloadUrl;
}
