package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.devops.pipeline.client.enums.FlowEventEnum;
import com.zto.devops.pipeline.client.model.release.enums.ReleasePlanActionEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/10/17
 * @Version 1.0
 */
@ZsmpModel(description = "上线计划编码入参")
@Data
public class VersionCodeReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "版本编码", required = true)
    private String versionCode;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @ZsmpModelProperty(description = "应用code", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "版本行为",required = false)
    private FlowEventEnum flowEvent;

    @ZsmpModelProperty(description = "操作环境",required = false)
    private EnvEnum env;

    @ZsmpModelProperty(description = "开始执行的动作", required = false)
    private ReleasePlanActionEnum targetAction;
}
