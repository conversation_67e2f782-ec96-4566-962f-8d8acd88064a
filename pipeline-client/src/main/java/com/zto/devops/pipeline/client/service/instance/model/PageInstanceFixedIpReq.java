package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 实例固定ip分页请求
 * @Author: renxinhui
 * @Date: 2024/11/12 15:37
 **/
@Data
@GatewayModel(description = "实例固定ip分页请求")
public class PageInstanceFixedIpReq extends PageQueryBase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实例编码
     */
    @Auth(type = AuthTypeConstant.INSTANCE)
    @ZsmpModelProperty(description = "实例编码", required = true)
    private String instanceCode;

    @ZsmpModelProperty(description = "搜索关键字")
    private String keywords;
}
