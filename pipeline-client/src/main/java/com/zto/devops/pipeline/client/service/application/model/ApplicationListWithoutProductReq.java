package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * ApplicationListWithoutProductReq
 *
 * <AUTHOR>
 * @date 2025-04-15 11:00
 */
@Data
public class ApplicationListWithoutProductReq extends PageQueryBase implements Serializable {
    /**
     * 应用类型id
     */
    @GatewayModelProperty(description = "应用类型id",required = false)
    private List<String> applicationTypeCode;

    /**
     * 应用名
     */
    @GatewayModelProperty(description = "应用id",required = false)
    private String name;

    /**
     * 应用id
     */
    @GatewayModelProperty(description = "应用id",required = false)
    private String appId;

    /**
     * 负责人userId
     */
    @GatewayModelProperty(description = "负责人userId",required = false)
    private List<Long> alerterIds;

    @GatewayModelProperty(description = "是否白名单",required = false)
    private List<Boolean> whiteList;
}
