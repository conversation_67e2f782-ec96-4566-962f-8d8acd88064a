package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @Date: 2023/10/25 15:21
 */
@ZsmpModel(description = "继续流水线执行-入参实体")
@Data
public class PipelineExecutionContinueReq implements Serializable {

    @ZsmpModelProperty(description = "流水线编码", sample = "1", required = true)
    private String executionCode;
}
