package com.zto.devops.pipeline.client.service.external.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: cher
 * @Date: 2023/12/26 17:32
 **/
@Data
@ZsmpModel(description = "查询动态多环境版本入参")
public class ListAutoEnvVersionReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "产品编码", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "按版本模糊搜索")
    private String versionKey;

}
