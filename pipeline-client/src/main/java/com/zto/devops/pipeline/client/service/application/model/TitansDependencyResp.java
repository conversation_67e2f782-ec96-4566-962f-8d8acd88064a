package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.pipeline.client.enums.TitansDependencyLevelEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2024/7/15
 */
@Data
public class TitansDependencyResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 依赖包id
     */
    @ZsmpModelProperty(description = "依赖包id", required = false)
    private String id;


    @ZsmpModelProperty(description = "依赖包名称", required = false)
    private String name;

    /**
     * 依赖包分组
     */
    @ZsmpModelProperty(description = "依赖包分组", required = false)
    private String group;

    @ZsmpModelProperty(description = "依赖包等级", required = false)
    private TitansDependencyLevelEnum level;

    /**
     * 依赖包描述
     */
    @ZsmpModelProperty(description = "依赖包描述", required = false)
    private String description;
}
