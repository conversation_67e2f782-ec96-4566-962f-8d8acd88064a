package com.zto.devops.pipeline.client.service.snapshot.model;

import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * snapshot resp
 *
 * <AUTHOR>
 * @date 2023-03-16 14:22
 */
@Getter
@Setter
public class SnapshotResp implements Serializable {
    private static final long serialVersionUID = 1L;
    @GatewayModelProperty( description = "快照编码", sample = "NS202101010001")
    private String code;
    @GatewayModelProperty( description = "快照名称", sample = "快照名称")
    private String name;
    @GatewayModelProperty( description = "快照描述", sample = "快照描述")
    private String description;
    @GatewayModelProperty( description = "快照环境", sample = "DEV")
    private EnvEnum env;
    @GatewayModelProperty( description = "快照明细", sample = "[]")
    private List<SnapshotPreviewResp> snapshotLines;

    @GatewayModelProperty(description = "创建人ID", sample = "1")
    private Long creatorId;
    @GatewayModelProperty(description = "创建人名称", sample = "1")
    private String creator;
    @GatewayModelProperty(description = "创建时间", sample = "1626586823000")
    private Date gmtCreate;
    @GatewayModelProperty(description = "更新人ID", sample = "1")
    private Long modifierId;
    @GatewayModelProperty(description = "更新人名称", sample = "1")
    private String modifier;
    @GatewayModelProperty(description = "更新时间", sample = "1626586823000")
    private Date gmtModified;
}
