package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * page all basic config query
 *
 * <AUTHOR> @date 2024-08-08 19:09
 */
@Data
public class PageAllBasicConfigReq extends PageQueryBase implements Serializable {
    @GatewayModelProperty(description = "key", required = false)
    private String key;

}
