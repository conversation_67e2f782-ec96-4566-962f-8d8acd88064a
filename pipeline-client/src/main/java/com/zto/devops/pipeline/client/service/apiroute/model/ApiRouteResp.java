package com.zto.devops.pipeline.client.service.apiroute.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ApiRouteResp implements Serializable {

    @GatewayModelProperty(description = "配置唯一业务编码")
    private String apiRouteCode;

    @GatewayModelProperty(description = "产品code")
    private String productCode;

    @GatewayModelProperty(description = "api名称",sample = "pipeline/apiroute/listApiRoute")
    private String strApiName;

    @GatewayModelProperty(description = "路由空间tag",sample = "fat")
    private String routeNamespaceTag;

    @GatewayModelProperty(description = "dubbo服务接口",sample = "dubbo service或者http url")
    private String dubboInterface;

    @GatewayModelProperty(description = "Dubbo接口方法名称",sample = "dubbo method")
    private String dubboMethod;

    @GatewayModelProperty(description = "接口类型",sample = "service类型：dubbo/http")
    private String serviceType;

    @GatewayModelProperty(description = "接口版本",sample = "1")
    private Long apiVersion;

}
