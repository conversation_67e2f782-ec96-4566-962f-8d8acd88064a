package com.zto.devops.pipeline.client.service.external.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/13
 */
@Data
public class UpdateUserVersionTagReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 版本tag
     */
    @ZsmpModelProperty(description = "版本tag", required = true)
    private String versionTag;

    @ZsmpModelProperty(description = "生效的appid清单")
    private List<String> appIdList;
}
