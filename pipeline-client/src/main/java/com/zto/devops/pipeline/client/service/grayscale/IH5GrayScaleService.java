package com.zto.devops.pipeline.client.service.grayscale;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.grayscale.entity.GrayscaleVO;
import com.zto.devops.pipeline.client.service.grayscale.model.*;

/**
 * h5 grayscale service
 *
 * <AUTHOR>
 * @date 2025-06-05 09:13
 **/
public interface IH5GrayScaleService {

    /**
     * h5灰度列表查询
     *
     * @param req req
     * @return applet gray scale list
     */
    PageResult<GrayscaleVO> pageH5List(PageGrayscaleReq req);

    /**
     * 编辑发布h5灰度
     *
     * @param req 编辑发布h5灰度入参
     * @return void
     */
    Result<Void> editPublishH5Version(H5GrayscaleReq req);

    /**
     * H5版本上线
     *
     * @param req H5版本上线入参
     * @return void
     */
    Result<Void> upH5Version(H5VersionUpReq req);

    /**
     * H5版本下线
     *
     * @param req H5版本下线入参
     * @return void
     */
    Result<Void> downH5Version(H5VersionDownReq req);

    /**
     * 为存量的h5应用创建对应的桥接应用
     * @param req req
     * @return void
     */
    Result<Void> createZmasBridgeApp(ZmasBridgAppReq req);

    /**
     * H5灰度页面详情
     * @param req req
     * @return vo
     */
    Result<GrayscaleVO> h5Detail(H5VersionEditDetailReq req);

    /**
     * 检查H5版本操作
     *
     * @param req 检查H5版本操作入参
     * @return 校验结果
     */
    Result<CheckH5VersionOperateResp> checkH5VersionOperate(CheckH5VersionOperateReq req);
}