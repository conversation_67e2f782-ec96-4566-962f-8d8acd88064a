package com.zto.devops.pipeline.client.service.release.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 移动变更项
 * @Date 2025/2/14
 * @Version 1.0
 */
@Data
public class ListMoveTargetReq implements Serializable {

    @GatewayModelProperty(description = "待移动的事件类型")
    private List<String> eventTypes;

    @GatewayModelProperty(description = "待移动的事件编码")
    private List<String> eventCodes;

    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "版本编码")
    private String versionCode;

    @GatewayModelProperty(description = "版本编码", sample = "MOVE, APPLY")
    private String action;

}
