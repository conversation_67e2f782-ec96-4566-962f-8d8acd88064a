package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2022/1/18
 */
@Data
public class InitNginxReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.INSTANCE)
    @GatewayModelProperty(description = "实例编码")
    @NotBlank(message = "实例编码不能为空")
    private String code;
}
