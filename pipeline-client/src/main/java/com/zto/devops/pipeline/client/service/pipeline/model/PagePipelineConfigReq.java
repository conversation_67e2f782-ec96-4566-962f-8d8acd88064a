package com.zto.devops.pipeline.client.service.pipeline.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;


@ZsmpModel(description = "流水线-配置列表")
@Data
public class PagePipelineConfigReq extends PageQueryBase implements Serializable {

    @ZsmpModelProperty(description = "产品编码", required = false)
    private String productCode;


    @ZsmpModelProperty(description = "关键字搜索", required = false)
    private String keyWord;

}
