package com.zto.devops.pipeline.client.service.application.model;


import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.simple.User;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.model.zmas.ZmasMobileAppBasicInfo;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@Data
public class AddApplicationReq implements Serializable {
    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "所属产品Code", required = false)
    private String productCode;
    @GatewayModelProperty(description = "产品名称", required = false)
    private String productName;
    @GatewayModelProperty(description = "应用别名", required = false)
    private String name;
    @GatewayModelProperty(description = "APPID", required = false)
    private String appId;
    @GatewayModelProperty(description = "apolloAppId", required = false)
    private String apolloAppId;
    @GatewayModelProperty(description = "应用类型", required = false)
    private String typeCode;
    @GatewayModelProperty(description = "应用描述", required = false)
    private String description;
    @GatewayModelProperty(description = "源码地址", required = false)
    private String gitProjectUrl;
    @GatewayModelProperty(description = "类型参数", required = false)
    private Map<String, Object> typeInput;
    @GatewayModelProperty(description = "基础镜像", required = false)
    private String baseImage;
    @GatewayModelProperty(description = "第一告警人", required = false)
    private User firstAlerter;
    @GatewayModelProperty(description = "第二告警人", required = false)
    private User secondAlerter;
    @GatewayModelProperty(description = "覆盖率标准值")
    private BigDecimal coverageStandardValue;
    @GatewayModelProperty(description = "是否白名单")
    private Boolean whiteList;
    @GatewayModelProperty(description = "白名单原因", required = false)
    private String whiteListReason;

    @ZsmpModelProperty(description = "是否使用模板")
    private Boolean useTemplate;
    @Auth(type = AuthTypeConstant.GIT_USER)
    @ZsmpModelProperty(description = "git组id")
    private Long groupId;
    @ZsmpModelProperty(description = "git组名")
    private String groupName;
    @ZsmpModelProperty(description = "工程标识，git项目名称")
    private String projectName;
    @GatewayModelProperty(description = "mavenProjectGroupId", required = false)
    private String mavenProjectGroupId;
    @GatewayModelProperty(description = "mavenProjectArtifactId", required = false)
    private String mavenProjectArtifactId;
    @GatewayModelProperty(description = "mavenProjectDomains", required = false)
    private List<String> mavenProjectDomains;
    @GatewayModelProperty(description = "泰坦版本id", required = false)
    private String mavenProjectTitansVersionId;
    @GatewayModelProperty(description = "泰坦依赖包id", required = false)
    private List<String> mavenProjectTitansDependencyIdList;
    @ZsmpModelProperty(description = "小程序发布渠道列表")
    private List<String> appletReleaseChannelList;
    @ZsmpModelProperty(description = "预热时间", required = false)
    private Integer warmupTime;

    @ZsmpModelProperty(description = "zmas移动应用基础信息", required = false)
    private ZmasMobileAppBasicInfo zmasMobileAppBasicInfo;


}
