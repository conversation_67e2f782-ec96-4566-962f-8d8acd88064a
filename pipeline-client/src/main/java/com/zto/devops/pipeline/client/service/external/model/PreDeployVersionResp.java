package com.zto.devops.pipeline.client.service.external.model;

import com.zto.zsmp.annotation.ZsmpModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ZsmpModel(description = "预发环境部署版本信息出参")
public class PreDeployVersionResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * appId
     */
    private String appId;
    /**
     * 生产域名
     */
    private String domainName;
    /**
     * 版本号
     */
    private String versionNum;
    /**
     * 版本名称
     */
    private String versionName;
    /**
     * 最近部署时间
     */
    private Date latestDeployTime;
}
