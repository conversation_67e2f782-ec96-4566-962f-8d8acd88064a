package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2021/8/10
 */
@Data
public class DeleteApplicationReq implements Serializable {
    /**
     * 应用编码
     */
    @Auth(type = AuthTypeConstant.APPLICATION)
    @GatewayModelProperty(description = "应用编码", required = false)
    private String code;
}
