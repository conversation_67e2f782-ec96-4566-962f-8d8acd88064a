package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2022/8/25
 * @Version 1.0
 */
@Setter
@Getter
public class ListGrayRuleReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 1L;
    private String productCode;

    private String name;

    private EnvEnum env;
}
