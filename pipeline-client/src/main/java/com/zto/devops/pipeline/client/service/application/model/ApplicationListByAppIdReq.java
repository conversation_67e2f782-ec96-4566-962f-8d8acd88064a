package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ApplicationListByAppIdReq extends PageQueryBase implements Serializable {
    @GatewayModelProperty(description = "appId",required = false)
    private String appId;
}
