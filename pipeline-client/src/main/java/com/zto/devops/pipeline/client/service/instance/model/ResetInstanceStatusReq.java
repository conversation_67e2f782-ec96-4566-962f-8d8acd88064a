package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.pipeline.client.enums.InstanceStatusEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/10/11
 * @Version 1.0
 */
@Data
public class ResetInstanceStatusReq implements Serializable {

    private static final long serialVersionUID = 1L;


    @GatewayModelProperty(description = "集群编码")
    @NotNull(message = "集群编码不能为空")
    private String code;

    @GatewayModelProperty(description = "实例状态")
    @NotNull(message = "实例状态不能为空")
    private InstanceStatusEnum status;
}
