package com.zto.devops.pipeline.client.service.pinpoint.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: cher
 * @Date: 2023/6/2 13:21
 **/
@Data
public class BugSeveritySummaryReq implements Serializable {

    private static final long serialVersionUID = 1L;
    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;
    @GatewayModelProperty(description = "项目ID")
    private String projectId;
    @GatewayModelProperty(description = "报告ID")
    private String reportId;
}
