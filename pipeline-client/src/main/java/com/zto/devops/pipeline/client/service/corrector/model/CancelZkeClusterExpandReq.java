package com.zto.devops.pipeline.client.service.corrector.model;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * 用于机房故障时，手动取消进行中的审批单
 * <AUTHOR>
 * @date 2025-03-10 16:54
 */
@Data
public class CancelZkeClusterExpandReq implements Serializable {

    /**
     * 是否添加副本数，true 扩容副本, false 新增实例
     */
    private Boolean updatePod;

    /**
     * 审批结果
     */
    private Boolean auditResult;


    /**
     * 审批不通过原因
     */
    private String notPassReason;

    /**
     * 审核单编码
     */
    private String auditNo;

    /**
     * 任务中心PC端审批链接
     */
    private String taskUrl;

    /**
     * 扩容的实例code
     */
    private String instanceCode;

}
