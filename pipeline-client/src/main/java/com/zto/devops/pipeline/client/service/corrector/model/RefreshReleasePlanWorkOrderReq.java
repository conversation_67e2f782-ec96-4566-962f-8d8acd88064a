package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RefreshReleasePlanWorkOrderReq implements Serializable {

    @ZsmpModelProperty(description = "版本号", sample = "VER1234579865", required = true)
    private String versionCode;

    @ZsmpModelProperty(description = "上线计划编号", sample = "EPN241104005008", required = true)
    private String releasePlanCode;

    @ZsmpModelProperty(description = "工单编号", sample = "AUD24110700F055", required = true)
    private List<String> workOrderCodes;
}
