package com.zto.devops.pipeline.client.service.application;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.framework.client.simple.Application;
import com.zto.devops.pipeline.client.model.application.entity.*;
import com.zto.devops.pipeline.client.model.application.entity.build.AppBuildConfigVO;
import com.zto.devops.pipeline.client.model.common.vo.DesktopPlatformVO;
import com.zto.devops.pipeline.client.model.common.vo.EnumRelateVO;
import com.zto.devops.pipeline.client.model.engine.zke.WayneBaseImageDTO;
import com.zto.devops.pipeline.client.model.flow.query.ListRelatedAppByVersionCodeQuery;
import com.zto.devops.pipeline.client.model.gitlab.GitBranchVO;
import com.zto.devops.pipeline.client.model.rpc.product.PipelineProductVO;
import com.zto.devops.pipeline.client.model.rpc.product.ProductMemberVO;
import com.zto.devops.pipeline.client.model.rpc.project.VersionVO;
import com.zto.devops.pipeline.client.model.rpc.user.UserSelectVO;
import com.zto.devops.pipeline.client.model.zmas.ZmasMergeAppVO;
import com.zto.devops.pipeline.client.model.zmas.ZmasMultiLanguageVO;
import com.zto.devops.pipeline.client.model.zmas.ZmasReactNativeVersionVO;
import com.zto.devops.pipeline.client.service.application.model.*;
import com.zto.devops.pipeline.client.service.cluster.model.ApplicationMigrateCheckReq;
import com.zto.devops.pipeline.client.service.cluster.model.ApplicationMigrateProductReq;
import com.zto.devops.pipeline.client.service.cluster.model.ApplicationMigrateReq;
import com.zto.devops.pipeline.client.service.gitlab.model.GitProjectEventReq;
import com.zto.devops.pipeline.client.service.instance.model.PreCheckResp;
import com.zto.devops.pipeline.client.service.req.FlowCodeReq;
import com.zto.devops.pipeline.client.service.req.ProductCodeOnlyReq;
import com.zto.devops.pipeline.client.service.req.ProductCodeReviewerReq;

import java.util.List;
import java.util.Set;

/**
 * @Author: chenguangyu
 * @Description: 应用服务接口
 * @Date: 2023/3/13 10:42
 */
public interface IApplicationService {

    /**
     * 创建应用
     */
    Result<Void> addApplication(AddApplicationReq req);

    Result<Void> updateApplication(EditApplicationReq req);

    Result<Void> deleteApplication(DeleteApplicationReq req);

    PageResult<ApplicationResp> applicationList(ApplicationListReq req);

    Result<List<ApplicationTypeVO>> applicationTypeList(ProductCodeOnlyReq req);

    Result<ArtifactDetailResp> artifactDetail(ArtifactDetailReq req);

    Result<List<PipelineProductVO>> getUserProduct(ApplicationMigrateProductReq applicationMigrateProductReq);

    Result<List<GitBranchVO>> listBranch(ListBranchReq req);

    Result<List<AppMergeRequestVO>> listMergeRequests(ListMergeRequestReq req);

    Result productApplication(ProductApplicationReq req);

    Result AllApplication(AllApplicationReq req);

    Result<ApplicationDetailResp> appDetail(AppDetailReq req);

    Result<ApplicationDetailResp> applicationDetail(ApplicationDetailReq req);

    Result<List<ApplicationTemplateVO>> templateList(ApplicationTemplateReq req);

    Result templateParam(AppTemplateParamReq req);

    Result<List<AppByGitIdVO>> findAppByGitCode(AppByGitIdReq req);

//    Result<String> getCookies(UserCookieReq req);

    Result<List<ApplicationNotNewestVO>> listNotNewestDeployApplication(ProductCodeOnlyReq req);

    Result<List<WayneResourceLimitResp>> getDockerFormat(ContainerFormatReq req);

    Result<List<WayneBaseImageDTO>> getImages(GetImagesReq req);

    Result<Void> pingGateway();

    //Result<GitMergeRequestVO> lastMergeRequest(FlowCodeReq req);

    PageResult<GitMergeRequestVO> pageMergeRequest(PageMergeRequestReq req);

    Result<List<GitProjectVO>> listGitProject(FlowCodeReq req);

    Result<List<ProductMemberVO>> listProductCodeReviewer(ProductCodeReviewerReq req);

    Result<List<UserSelectVO>> listApplicationAlerter(ApplicationDetailReq req);

    Result<List<TitansVersionResp>> listTitansVersion();

    Result<List<TitansDependencyResp>> listTitansDependency(String titansVersionId);

    /************************************  给外域使用 **************************************************/
    List<ApplicationVO> listApplicationByProductCode(String productCode);  // product

    List<ApplicationVO> listApplicationByProductCodes(List<String> productCodes);  // products

    List<Application> listRelatedAppByVersionCodeQuery(ListRelatedAppByVersionCodeQuery query);

    List<ApplicationVO> listApplicationByCodes(List<String> codes);  // user

    List<ApplicationVO> listApplicationByAppIds(List<String> appIds);  // qc

    /**
     * 迁移应用
     *
     * @param req
     * @return
     */
    Result<Void> applicationMigration(ApplicationMigrateReq req);

    /**
     * 创建合并请求
     *
     * @param req
     * @return
     */
    Result<Void> createMergeRequest(CreateMrReq req);

    /**
     * 前端切换应用类型为W3C
     *
     * @param req
     * @return
     */
    Result<Void> switchFrontApplicationType(ApplicationDetailReq req);

    /**
     * 前端切换应用类型为W3C补偿历史包上传记录
     *
     * @param req
     * @return
     */
    Result<Void> switchFrontAppTypeCompensate(SwitchFrontAppTypeCompensateReq req);

    /**
     * zss切换应用类型为titans-service
     *
     * @param req
     * @return
     */
    Result<Void> switchApplicationType(EditApplicationReq req);

    /**
     * W3C应用(除portal-main-web门户主应用)刷grayscaleSwitch=1
     *
     * @return
     */
    Result<Integer> freshApplicationGrayscaleSwitchForW3C(List<String> codes);

    /**
     * 增量同步git项目
     *
     * @param req
     * @return
     */
    Result syncGitProjectWebhook(GitProjectEventReq req);

    /**
     * 全量同步git项目
     */
    Result syncAllGitProject();

    /**
     * 关闭合并请求
     *
     * @param req
     * @return
     */
    Result<Void> closeMr(CloseMergeReq req);

    /**
     * 应用关闭灰度按钮校验
     *
     * @param req
     * @return
     */
    Result<PreCheckResp> closeGrayscaleSwitchCheck(ApplicationCodeReq req);

    Result<Void> syncApplicationToCmdb();

    Result<Void> syncApplicationAlerter2GitMaintainer(List<String> codes);

    /**
     * 同步普通应用最新的一次部署到oss新目录
     *
     * @return void
     */
    Result<Void> syncNormalFrontEndApplicationOss();

    /**
     * 刷新oss实例和小程序实例
     * h5应用集群保证空间oss和小程序实例各有一个
     * PC应用/静态资源集群保证仅有一个空间oss实例
     * 门户微应用/静态资源-W3C集群保证所有空间仅有一个灰度oss实例，动态空间还有一个空间oss实例
     *
     * @return void
     */
    Result<Void> addOssOrAppletInstance4FrontAppCluster();

    /**
     * h5应用集群保证空间灰度oss实例有一个
     *
     * @return void
     */
    Result<Void> syncHFiveGrayOss(List<String> appCodes);

    /**
     * 同步oss上前端资源到oss新路径
     *
     * @return
     */
    Result<Void> syncFrontOssToNewPath();

    /**
     * 核心应用开关
     *
     * @param req application switch req
     * @return void
     */
    Result<Void> coreApplicationSwitch(CoreApplicationSwitchReq req);

    /**
     * 核心应用列表查询
     *
     * @param req req
     * @return list
     */
    PageResult<ApplicationResp> coreApplicationListQuery(CoreApplicationListReq req);

    /**
     * 刷数据：oss实例类型标记
     *
     * @param dynamicNamespaceAddOssInstance 是否给动态多环境添加oss实例
     * @return
     */
    Result<Void> refreshOssInstance(boolean dynamicNamespaceAddOssInstance);

    Result<Void> refreshJenkinsPinpointJob(Set<String> applicationCodeList);

    Result<Void> refreshFlowApplicationCommitIdJob(Set<String> flowCodeList);

    Result<Void> syncUpdateApplicationToCmdb(String productCode);

    /**
     * 同步所有应用创建容器环境的jenkins-job
     */
    Result<Void> syncAppToJenkinsDockerJob(List<String> codes);

    /**
     * 迁移应用前置检验
     *
     * @param req
     * @return
     */
    Result<Void> applicationMigrationPreCheck(ApplicationMigrateCheckReq req);

    /**
     * 取消核心应用标记前置校验
     *
     * @param req req
     * @return void
     */
    Result<Void> coreApplicationOffPerCheck(CoreApplicationOffPreCheckReq req);

    /**
     * 隐藏页面查询应用类型
     *
     * @param name name
     * @return app type list
     */
    Result<List<ApplicationTypeVO>> applicationTypeListWithoutProduct(String name);

    /**
     * 隐藏页面，给启动参数页面使用
     * @return list
     */
    Result<List<EnumRelateVO>> applicationTypeListWithoutProductForStartupParam();

    /**
     * zmas应用关联的发布渠道
     *
     * @return list
     */
    Result<List<ZmasMergeAppVO>> appRelateReleaseChannel();

    PageResult<ApplicationResp> applicationSummary(ApplicationSummaryReq req);

    /**
     * 导出应用汇总数据
     *
     * @param req {@link ApplicationSummaryReq}
     */
    Result<Void> applicationSummaryExport(ApplicationSummaryReq req);

    /**
     * 返回核心应用拦截模式
     * @return list
     */
    Result<List<EnumRelateVO>> queryAllCoreApplicationIsolationModel();

    /**
     * 入参不需要product code
     * @param req req
     * @return resp
     */
    PageResult<ApplicationResp> applicationListWithoutProduct(ApplicationListWithoutProductReq req);

    /**
     * 根据appId模糊查询应用列表
     * @param req 请求
     * @return 应用列表
     */
    Result<List<ApplicationSimpleVO>> listApplicationByAppId(ApplicationListByAppIdReq req);

    /**
     * 返回zmas多语言列表
     * @return list
     */
    Result<List<ZmasMultiLanguageVO>> zmasMultiLanguageList();

    /**
     * 为了区分页面tab，提供接口返回应用的类型
     * @param req req
     * @return app type
     */
    Result<ApplicationTypeVO> queryApplicationTypeByAppId(AppTypeQueryReq req);

    /**
     * 除了master分支之外的主分支是否存在
     * @param req req
     * @return void
     */
    Result<Void> checkZmasMainBranch(CheckZmasMainBranchReq req);

    /**
     *修改应用构建配置信息
     * */
    Result<Void> editAppBuildConfig(AppBuildConfigReq req);

    /**
     * 获取应用构建配置信息
     * */
    Result<AppBuildConfigVO> getAppBuildConfig(GetAppBuildConfigReq req);

    /**
     * 根据git地址和关键字查分支
     * @param req req
     * @return list
     */
    Result<List<GitBranchVO>> listGitBranch(ListGitBranchReq req);

    /**
     * 查询zmas mobile app对应的已关联的版本
     * @param req req
     * @return list
     */
    Result<List<VersionVO>> appRelateVersionList(AppRelateVersionListReq req);

    /**
     * 判断某产品下是否存在指定类型的应用
     * @param req 请求参数，包含产品编码和应用类型列表
     * @return 是否存在
     */
    Result<Boolean> existsApplicationType(ExistsApplicationTypeReq req);

    /**
     * 获取zmas rn版本号
     * @return list
     */
    Result<List<ZmasReactNativeVersionVO>> getZmasRNVersionList();

    /**
     * zmas应用
     * @return upload token
     */
    Result<String> getZmasFileUploadToken();

    /**
     * 为上传文件生成签名，前端不好实现sha-1
     * @param req
     * @return
     */
    Result<ZmasUploadSignResp> getZmasFileUploadSign(ZmasUploadSignReq req);

    /**
     * 获取桌面应用平台枚举
     * @return 桌面应用平台枚举列表
     */
    Result<List<DesktopPlatformVO>> getDesktopPlatformEnum();

}
