package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.simple.Field;
import com.zto.devops.pipeline.client.enums.*;
import com.zto.devops.pipeline.client.enums.basicconfig.CoreApplicationIsolationModelEnum;
import com.zto.devops.pipeline.client.model.application.entity.ApplicationTypeVO;
import com.zto.devops.pipeline.client.model.common.model.GeneralVO;
import com.zto.devops.pipeline.client.model.instance.entity.InstancePodVO;
import com.zto.devops.pipeline.client.model.instance.entity.WayneDetailVO;
import com.zto.devops.pipeline.client.model.instance.entity.ZCloudDetailVO;
import com.zto.devops.pipeline.client.model.simple.TagDO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class PageInstanceResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实例编码
     */
    private String code;

    /**
     * 实例名称
     */
    private String name;

    /**
     * 应用集群编码
     */
    private String clusterCode;

    /**
     * 空间编码
     */
    private String namespaceCode;

    /**
     * 应用编码
     */
    private String applicationCode;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * appId
     */
    private String appId;

    /**
     * 应用类型
     */
    private ApplicationTypeVO applicationType;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 部署类型
     */
    private DeployTypeEnum deployType;

    /**
     * 部署参数是否变化(上次部署成功的与预计下次部署的是否一样)
     */
    private Boolean runningDeploymentParamChanged;
    /**
     * 部署参数
     */
    private String runningDeploymentParam;
    /**
     * 部署参数(再次部署将会生效的，实时算出来的)
     */
    private String pendingDeploymentParam;
    /**
     * 部署参数
     */
    private String deploymentParam;

    /**
     * 默认部署参数(不可修改)
     */
    private String defaultDeploymentParam;

    /**
     * 部署路径
     */
    private String deployPath;

    /**
     * 主机id
     */
    private String hostId;

    /**
     * wayne实例id
     */
    private Long wayneDeploymentId;

    /**
     * 主机来源
     */
    private String computerSource;

    /**
     * 机房 : 湖州/无锡/吴兴/阿里云国际/阿里云金融
     */
    private ComputerRoomEnum computerRoom;

    /**
     * 类型 : VM/容器
     */
    private MachineTypeEnum type;

    /**
     * 主机ip
     */
    private String ip;

    /**
     * 服务端口
     */
    private String port;

    /**
     * 主机SaltMinionId
     */
    private String minionId;

    /**
     * 环境标签
     */
    private EnvEnum env;

    /**
     * 健康检查端口
     */
    private String healthyPort;

    /**
     * jacoco覆盖率检查端口
     */
    private String jacocoPort;

    /**
     * 最近一次部署人
     */
    private String deployer;

    /**
     * 最近一次部署时间
     */
    private Date deploymentTime;

    /**
     * 包仓库
     */
    private String commit;

    /**
     * 包仓库url
     */
    private String commitUrl;

    /**
     * 制品code
     */
    private String artifactCode;

    /**
     * 制品名称
     */
    private String artifactName;

    /**
     * 制品构建时间
     */
    private Date artifactBuildTime;

    private String gitUrl;

    /**
     * 实例状态
     */
    private InstanceStatusEnum status;

    /**
     * wayne信息
     */
    private WayneDetailVO wayneDetail;

    /**
     * zCloud信息
     */
    private ZCloudDetailVO zCloudDetail;

    /**
     * 状态机
     */
    private List<GeneralVO> ops;

    /**
     * 可编辑字段
     */
    private List<Field> fieldVOS;

    /**
     * 状态描述
     */
    private String statusDesc;

    private String computeRoomDesc;

    private String envDesc;

    /**
     * 短commit
     */
    private String shortCommit;

    /**
     * 标签
     */
    private List<TagDO> tags;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 类型描述
     */
    private String typeDesc;


    private Map<String, Object> typeInput;
    private String apolloSecret;
    private String serverXmlName;
    /**
     * 停止命令参数(可编辑的)
     */
    private String stopParam;

    private GrayTagEnum grayTag;
    private String grayTagName;

    private String instanceTagName;
    private String instanceTagCode;
    private Boolean enable;
    /**
     * 流程(版本)名称
     */
    private String flowName;

    /**
     * 空间类型
     */
    private NamespaceTypeEnum namespaceType;

    /**
     * 空间名称
     */
    private String namespaceName;

    /**
     * 是否是核心实例
     */
    private Boolean coreInstance;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 更新时间
     */
    private Date gmtModified;

    private String cpuLoad;// cpuCount

    private String memoryMargin;// memory

    // 磁盘
    private String diskMargin;// disk

    private String versionNum;

    private InstancePodVO instancePodVO;
    //@GatewayModelProperty(description = "网络模式.上报模式:CHECK;拦截模式:INTERCEPT", sample = "")
    private CoreApplicationIsolationModelEnum networkType;
    private String networkTypeName;

    private Boolean isFixedIp;

}
