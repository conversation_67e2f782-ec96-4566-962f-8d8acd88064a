package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PageInstanceForAppIpViewReq extends PageQueryBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "环境", required = true)
    private String env;
    @ZsmpModelProperty(description = "应用code", required = true)
    private String applicationCode;
    @ZsmpModelProperty(description = "搜索关键字")
    private String keywords;
}
