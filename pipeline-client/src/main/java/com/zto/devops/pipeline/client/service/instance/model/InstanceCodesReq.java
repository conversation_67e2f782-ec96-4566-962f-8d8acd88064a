package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/3/22
 * @Version 1.0
 */
@Data
@ZsmpModel(description = "实例编码集合入参")
public class InstanceCodesReq implements Serializable {

    private static final long serialVersionUID = 1L;


    @Auth(type = AuthTypeConstant.INSTANCE_LIST)
    @ZsmpModelProperty(description = "实例编码集合", required = true)
    private List<String> codes;


}
