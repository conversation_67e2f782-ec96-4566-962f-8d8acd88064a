package com.zto.devops.pipeline.client.service;

import com.zto.devops.framework.client.dto.PageObjectResult;
import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.application.entity.build.BuildBranchVO;
import com.zto.devops.pipeline.client.model.flow.command.batch.*;
import com.zto.devops.pipeline.client.model.flow.entity.*;
import com.zto.devops.pipeline.client.model.flow.entity.check.CheckVO;
import com.zto.devops.pipeline.client.model.flow.query.FindVersionFlowDateQuery;
import com.zto.devops.pipeline.client.model.flow.query.VersionReleaseBranchCommitQuery;
import com.zto.devops.pipeline.client.service.flow.model.*;
import com.zto.devops.pipeline.client.service.req.*;
import com.zto.devops.pipeline.client.service.req.FlowLaneCardQueryReq;
import com.zto.devops.pipeline.client.service.req.FlowLaneCardResp;
import com.zto.devops.pipeline.client.service.req.flow.EditVersionReleasePlanReq;
import com.zto.devops.pipeline.client.service.req.flow.FlowApplicationDetailByVersionCodeReq;
import com.zto.devops.pipeline.client.service.req.flow.FlowMergeCodeReq;
import com.zto.devops.pipeline.client.service.req.flow.FlowOnlyCodeReq;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yangzhongru
 * 2023/2/21 09:38
 */
public interface IFlowService {

    /**
     * 流水线域-发布流程-部署
     *
     * @param req
     * @return
     */
    Result<Void> deploy(DeployReq req);


    /**
     * 部署的分支commit检查
     * 如果有 最新的 commit 没有部署过，则直接范围false
     *
     * @return
     */
    Result<DeployBranchCheckResp> deployBranchCommitCheck(DeployBranchCheckReq req);

    /**
     * 提测
     *
     * @param req
     * @return
     */
    Result<Void> submitTest(FlowCodeReq req);

    /**
     * 冒烟报告
     *
     * @param req
     * @return
     */
    Result<Void> smokeReport(SmokeReportReq req);

    /**
     * 测试报告
     *
     * @param req
     * @return
     */
    Result<Void> testReport(TestReportReq req);


    /**
     * 退回
     *
     * @param req
     * @return
     */
    Result<Void> back(BackFlowReq req);

    /**
     * 验收结果
     *
     * @param req
     * @return
     */
    Result<Void> acceptReport(AcceptReportReq req);

    /**
     * 归档
     *
     * @param req
     * @return
     */
    Result<Void> archive(FlowCodeReq req);

    /**
     * 取消置顶
     *
     * @param req
     * @return
     */
    Result<Void> cancelTop(FlowCancelTopReq req);

    /**
     * 置顶
     *
     * @param req
     * @return
     */
    Result<Void> toTop(FlowToTopReq req);

    /**
     * 批次拆分
     *
     * @param req
     * @return
     */
    Result<Void> deployBatchSplit(DeployBatchSplitReq req);

    /**
     * 查看发布策略
     *
     * @param req
     * @return
     */
    Result<FlowDeployStrategyResp> deployStrategy(DeployStrategyReq req);

    /**
     * 查看编辑申请发布的发布策略、生产应用实例列表、流程审核单
     *
     * @param req
     * @return
     */
    Result<FlowApplyReleaseVO> queryFlowApplyRelease(DeployStrategyReq req);

    /**
     * 根据流程获取部署分支
     *
     * @param req req
     * @return result
     */
    Result<List<FlowDeployBranchResp>> deployBranchByFlow(FlowCodeReq req);

    /**
     * 流程详情
     *
     * @param req req
     * @return result
     */
    Result<FlowDetailResp> flowDetail(FlowCodeReq req);

    /**
     * 查询流程已关联应用列表
     *
     * @param req req
     * @return result
     */
    Result<QueryFlowRelatedApplicationResp> flowRelatedAppList(DeployApplicationReq req);

    /**
     * 是否是定制产品
     *
     * @param req req
     * @return result
     */
    Result<CustomProductResp> isCustomProduct(ProductCodeReq req);

    /**
     * jacoco关闭是否测试
     *
     * @param req req
     * @return result
     */
    Result<FlowUserResp> userRole(FlowJacocoUserReq req);

    /**
     * 流程列表(泳道)
     *
     * @param req req
     * @return result
     */
    Result<List<FlowLaneResp>> queryFlowLaneInfo(FlowLaneQueryReq req);

    /**
     * 查询流程泳道卡片信息
     *
     * @param req req
     * @return result
     */
    Result<FlowLaneCardResp> queryFlowLaneCard(FlowLaneCardQueryReq req);

    /**
     * 应用实例列表
     *
     * @param req req
     * @return list
     */
    Result<List<FlowRelatedAppAndInstanceVO>> listRelatedAppAndInstances(DeploySequenceAndRateReviewReq req);

    /**
     *  查询关联子流程列表
     *
     * @param req req
     * @return list
     */
    Result<List<RelatedFlowResp>> relatedFlowList(FlowCodeReq req);

    /**
     * 关联应用之前已创建分支的应用提示
     *
     * @param req req
     * @return list
     */
    Result<List<RelateAppBranchTipVO>> relateAppBranchTip(RelateApplicationReq req);

    /**
     * 查询流程关联应用列表
     *
     * @param req req
     * @return result
     */
    Result<QueryFlowRelateApplicationResp> flowApplicationList(FlowCodeReq req);

    /**
     * 查询版本空间域名
     *
     * @param req req
     * @return result
     */
    Result<RelateDomainVO> relateDomain(FlowCodeReq req);

    /**
     * 查询版本空间标签
     *
     * @param req req
     * @return list
     */
    Result<List<RelateNamespaceVO>> relateNamespace(FlowCodeReq req);

    /**
     * 待回归列表
     *
     * @param req req
     * @return list
     */
    Result<List<FlowVO>> queryWaitRegressLane(FlowLaneQueryReq req);


    /**
     * 申请发布
     *
     * @param req
     * @return
     */
    Result<Void> applyRelease(ApplyReleaseReq req);

    /**
     * 编辑发布计划
     *
     * @param req
     * @return
     */
    Result<Void> editApplyRelease(ApplyReleaseReq req);

    /**
     * 申请灾备接口
     */
    public Result<Void> backupApplyRelease(ApplyReleaseReq req);

    /**
     * 获取审核单的默认信息
     *
     * @param req
     * @return
     */
    Result<FlowAuditFieldResp> auditField(FlowCodeReq req);

    /**
     * 撤销申请
     *
     * @param req
     * @return
     */
    Result<Void> cancelApplyRelease(FlowCodeReq req);

    /**
     * 取消发布
     *
     * @param req
     * @return
     */
    Result<Void> cancelRelease(FlowCodeReq req);

    /**
     * 操作日志
     *
     * @param req req
     * @return result
     */
    PageObjectResult<FlowActionLogResp> listActionLogs(FlowLogReq req);

    /**
     * 检测版本测试报告
     *
     * @param req req
     * @return version test report
     */
    // dubbo服务治理,待清理
//    Result<VersionTestReportResp> checkTestReport(FlowCodeReq req);

    /**
     * 代码检查类型
     *
     * @return check type
     */
    Result<List<ApplicationCheckTypeResp>> codeCheckType();

    /**
     * 查询代码安全扫描结果
     *
     * @param req req
     * @return flow application scan vo
     */
    Result<List<FlowApplicationScanVO>> listCodeScan(FlowCodeReq req);

    /**
     * 归档列表
     *
     * @param req req
     * @return page
     */
    PageResult<PageFlowResp> pageFlow(PageFlowReq req);

    /**
     * 审批结果单回调
     *
     * @param req
     * @return
     */
    Result<Void> audit(ProcessAuditReq req);

    /**
     * 冲突列表
     */
    Result<List<MergeConflictVO>> mergeConflictList(FlowCodeReq req);

    /**
     * 解决冲突
     */
    Result<Void> mergeConflictSolve(MergeConflictSolveReq req);

    /**
     * 重建分支
     */
    Result<Void> rebuildBranch(RebuildBranchReq req);

    /**
     * 回归测试
     */
    Result<Void> regressTest(RegressTestReq req);

    /**
     * 回归结果
     */
    Result<Void> regressResult(DeliveryReportReq req);

    /**
     * 关联应用
     *
     * @param req
     * @return
     */
    Result<Void> relateApplication(RelateApplicationReq req);


    VersionFlowDateVO findVersionFlowDateQuery(FindVersionFlowDateQuery query);

    /**
     * 合并代码
     *
     * @param req
     * @return
     */
    Result<Void> mergeCode(FlowCodeReq req);

    /**
     * 版本代码合并
     *
     * @param req
     * @return
     */
    Result<Void> flowMergeCode(FlowMergeCodeReq req);

    /**
     * 发布
     *
     * @param req
     * @return
     */
    Result<Void> release(ReleaseReq req);

    /**
     * 代码检查
     *
     * @param req
     * @return
     */
    Result<Void> codeCheck(ApplicationCheckReq req);

    /**
     * 中断批次发布
     */
    Result<Void> abortBatch(AbortBatchReq req);

    /**
     * 继续批次发布
     */
    Result<Void> continueBatch(ContinueBatchReq req);

    //申请发布时，检查要发布的包是否最新
    Result<List<PreCheckMessageVO>> applyReleaseCheck(FlowCodeReq req);

    Result<List<FlowApplicationScanVO>> listPinpointScanResult(ListCodeScanReq req);

    /**
     * 同步版本release分支
     */
    Result syncFlowRelease();

    /**
     * 新的流水线，查询流程详情
     *
     * @param req req
     * @return result
     */
    Result<FlowBaseDetailVO> queryFlowDetail(FlowVersionCodeReq req);

    /**
     * 查询简版流程
     *
     * @param req req
     * @return result
     */
    Result<SimpleFlowVO> querySimpleFlow(FlowVersionCodeReq req);

    /***
     *  查询版本关联应用详情信息
     * */
    Result<List<FlowApplicationDetailVO>> listFlowApplicationDetail(FlowApplicationDetailReq req);


    /**
     * 根据version code，查询对应的操作列表
     *
     * @param req req
     * @return list
     */
    VersionButtonVO queryOperateListByVersionCode(VersionButtonReq req);

    /**
     * 根据version code查询所有相关的的flow code
     *
     * @param versionCode version code
     * @return flow code list
     */
    List<String> queryFlowCodeListByVersionCode(String versionCode);

    /**
     * 查询版本概览页面的统计数据
     * project 调用
     */
    FlowSummaryVO queryVersionOverView(String versionCode);

    List<FlowVO> getFlowByVersionCodes(List<String> list);

    /**
     * 校验release是否包含feature分支
     *
     * @param req
     * @return
     */
    Result<List<CheckReleaseResp>> checkReleaseContainsFeature(FlowCodeReq req);

    /**
     * 合并feature到release分支
     * 创建feature到release分支的mr，根据是否开启代码审查自动合并代码
     *
     * @param req
     * @return
     */
    Result<Void> mergeFeatureToRelease(MergeFeatureToReleaseReq req);

    /**
     * 手动部署时校验正在进行中的应用
     * @param req
     * @return
     */
    Result<CheckDoingFlowVO> checkDoingFlow(CheckDoingFlowReq req);

    Result addApplicationNum(ApplicationNumReq req);

    /**
     * 开始自测
     *
     * @param req
     * @return
     */
    Result<Void> startSelfTest(FlowCodeReq req);

    List<FlowVO> getFlowAndLastFlowBatchByVersionCodes(List<String> list);

    Result<List<CheckVO>> listFlowCheck(FlowCheckReq req);

    Result<Void> submitFlowCheck(FlowCheckSubmitReq req);

    // 解冻流程
    Result<Void> unfreeze(FlowUnfreezeReq req);

    /**
     * 根据分支名称，查询版本code（devops-middleware）
     *
     * @param req {@link  QueryVersionCodeReq}
     * @return {@link Map} branchName versionCode
     */
    Map<String, String> getVersionCodeByBranchNames(QueryVersionCodeReq req);

    Result<List<String>> findOtherDeployedApplicationRelatedToArtifact(FindOtherDeployedApplicationRelatedToArtifactReq req);

    Result<List<ApplyRecordTypeVO>> listApplyRecordType();

    /**
     * 上线计划的批次拆分
     *
     * @param command
     * @return
     */
    void addBatch(AddBatchCommand command);

    /**
     * 上线计划取消批次拆分
     *
     * @param command
     */
    void cancelBatch(CancelBatchCommand command);

    /**
     * 上线计划发布批次
     *
     * @param command
     * @return executionCode 执行编码
     */
    String publishBatch(PublishBatchCommand command);

    /**
     * 上线计划中断发布批次
     *
     * @param command
     */
    void abortBatch(AbortBatchCommand command);

    /**
     * 上线计划继续发布批次
     *
     * @param command
     */
    void continueBatch(ContinueBatchCommand command);


    /**
     * 根据version code查询flow关联的应用详情
     * @param req req req
     * @return flow application detail
     */
    Result<List<FlowApplicationDetailVO>> listFlowApplicationDetailByVersionCode(FlowApplicationDetailByVersionCodeReq req);

    /**
     * 是否开启了动态多环境
     * @param productCode
     * @return true/false
     */
    Boolean autoEnvEnable(String productCode);

    Result<QueryFlowRelateApplicationResp> queryAppIdByFlowCode(QueryAppIdByFlowCodeReq req);

    /**
     * 大版本的子版本列表
     *
     * @param req
     * @return
     */
    Result<List<SubFlowVO>> listSubFlows(FlowOnlyCodeReq req);

    Result<Void> editVersionReleasePlan(EditVersionReleasePlanReq req);

    /**
     * @param req
     * @return 大版本的子版本列表
     */
    List<FlowBaseDetailVO> listSubFlowsByVersionCode(FlowVersionCodeReq req);

    Result<List<RelatedFlowResp>> relatedFlowListDoc(String flowCode);

    Result<List<FlowBranchVO>> listFlowBranch(FlowBranchQueryReq req);

    /**
     * 根据appid查询流程版本
     * @param appId appid
     * @return list
     */
    List<String> queryVersionCodeListByAppId(String appId);

    String queryVersionReleaseBranchCommit(VersionReleaseBranchCommitQuery query);

    /**
     *  构建
     */
    Result<Void> buildMobileApp(BuildMobileAppReq req);

    /**
     * 获取指定版本下所有移动端应用的最新制品包
     *
     * @param req 请求参数
     * @return 移动端应用最新制品包列表
     */
    Result<List<MobileAppArtifactResp>> listLatestMobileAppArtifacts(GetLatestMobileAppArtifactReq req);

    /**
     * 获取指定版本下指定应用的最新构建参数
     *
     * @param req 请求参数
     * @return 最新构建参数
     */
    Result<LatestBuildParamVO> getLatestBuildParam(GetLatestBuildParamReq req);


    /**
     * 获取可用于构建的分支
     * @return
     */
    Result<List<BuildBranchVO>> listAvailableBranchesForMobileBuild(ListMobileBuildBranchReq req);

    /**
     *  部署
     */
    Result<Void> deployMobileApp(DeployMobileAppReq req);
}
