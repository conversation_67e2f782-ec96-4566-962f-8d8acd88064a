package com.zto.devops.pipeline.client.service.grayscale;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.gray.entity.*;
import com.zto.devops.pipeline.client.service.grayscale.model.*;

import java.util.List;
import java.util.Map;

/**
 * @Author: chenguangyu
 * @Description:
 * @Date: 2023/3/15 10:54
 */
public interface IGrayRuleService {

    Result<Void> add(AddGrayRuleReq req);

    Result<Void> modify(ModifyGrayRuleReq req);

    Result<Void> delete(DeleteGrayRuleReq req);

    PageResult<GrayRuleVO> page(ListGrayRuleReq req);

    Result<GrayRuleVO> find(FindGrayRuleReq req);

    Result<List<AreaInfoVO>> queryAreas(QueryAreaInfoReq req);

    Result<List<UserInfoVO>> queryUser(QueryUserInfoReq req);

    Result<List<SiteInfoVO>> querySite(QuerySiteInfoReq req);

    /**
     * query all gray rules
     *
     * @param req req
     * @return result
     */
    com.zto.titans.common.entity.Result<Map<String, List<GrayRuleSdkVO>>> listAllGrayRules(ListGrayRuleReq req);

    /**
     * 对外-获取产品下规则列表【不用上生产】
     *
     * @param productCode product code
     * @return result
     */
    com.zto.titans.common.entity.Result<List<GrayRuleInfo>> listGrayRulesByProductCode(String productCode);

    /**
     * 对外-获取动态多环境门户微应用域名
     *
     * @param req req
     * @return result
     */
    com.zto.titans.common.entity.Result<List<MicroDomainResp>> listMicroDomain(ListMicroDomainReq req);
}
