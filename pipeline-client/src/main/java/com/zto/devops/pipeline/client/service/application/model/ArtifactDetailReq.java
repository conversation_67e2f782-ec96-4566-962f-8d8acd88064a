package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2021/8/13
 */
@Data
public class ArtifactDetailReq implements Serializable {

    private static final long serialVersionUID = 1L;


    @Auth(type = AuthTypeConstant.ARTIFACT)
    @GatewayModelProperty(description = "制品编码")
    private String code;
}
