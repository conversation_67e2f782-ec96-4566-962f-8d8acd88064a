package com.zto.devops.pipeline.client.service.startup.model;

import com.zto.devops.pipeline.client.model.startupparam.vo.StartupParamConditionValueVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * add startup param req
 *
 * <AUTHOR>
 * @date 2025-04-11 10:01
 */
@Data
public class AddStartupParamRuleReq implements Serializable {
    @GatewayModelProperty( description = "名称", sample = "兔喜专用")
    private String name;

    @GatewayModelProperty( description = "优先级", sample = "1")
    private Integer priority;

    @GatewayModelProperty( description = "模板(即启动参数)", sample = "-Da=b")
    private String deploymentParam;

    @GatewayModelProperty( description = "启动参数匹配条件表达式列表,用于计算启动参数", sample = "1")
    private List<StartupParamConditionValueVO> startupParamConditionValueList;

}
