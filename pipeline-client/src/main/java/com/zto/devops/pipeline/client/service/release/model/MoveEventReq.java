package com.zto.devops.pipeline.client.service.release.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 移动变更项
 * @Date 2025/2/14
 * @Version 1.0
 */
@Data
public class MoveEventReq implements Serializable {

    @GatewayModelProperty(description = "事件编码")
    private List<String> codes;

    @GatewayModelProperty(description = "目标分组", required = false)
    private String targetParent;

    @GatewayModelProperty(description = "是否需要新建分组，并移动到新的分组中", required = false)
    private Boolean newParentAndMoveIn = false;

    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "版本编码")
    private String versionCode;

}
