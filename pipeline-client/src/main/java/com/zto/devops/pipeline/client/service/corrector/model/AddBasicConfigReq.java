package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * add basic config req
 *
 * <AUTHOR>
 * @date 2024-08-08 16:40
 */
@Data
public class AddBasicConfigReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "key", sample = "[]", required = true)
    private String key;

    @ZsmpModelProperty(description = "value", sample = "[]", required = true)
    private String value;

    @ZsmpModelProperty(description = "desc", sample = "[]")
    private String desc;
}
