package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.devops.pipeline.client.enums.FlowBusinessTypeEnum;
import com.zto.devops.pipeline.client.model.flow.entity.DeployStrategyVO;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@ZsmpModel(description = "批次拆分信息")
@Data
public class SaveDeployStrategyReq implements Serializable {

    @ZsmpModelProperty(description = "批次信息", required = true)
    List<DeployStrategyVO> deployStrategy;

    @ZsmpModelProperty(description = "产品编码", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "版本编码", required = true)
    private String versionCode;

    @ZsmpModelProperty(description = "流程编码", required = true)
    private String flowCode;

    @ZsmpModelProperty(description = "流程类型", required = true)
    private FlowBusinessTypeEnum flowBusinessType;

    @ZsmpModelProperty(description = "批次的环境")
    private EnvEnum env;
}
