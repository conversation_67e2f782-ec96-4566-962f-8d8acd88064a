package com.zto.devops.pipeline.client.service.apiroute.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * api网关/流量网关查询参数
 *
 * <AUTHOR>
 * @date 2024-10-23 14:30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ListGatewayApiRouteReq extends PageQueryBase implements Serializable {
    @GatewayModelProperty(description = "一站式生成的产品code", required = false)
    private String productCode;

    @GatewayModelProperty(description = "路由空间搜索", required = false)
    private List<String> routeNamespaceCodeList;

    @GatewayModelProperty(description = "api名称/api描述/标签/appid", required = false)
    private String search;

    @GatewayModelProperty(description = "是否配置", required = false)
    private Boolean hadConfig;

    @ZsmpModelProperty(description = "appId列表", sample = "1")
    private List<String> appIdList;

    @GatewayModelProperty(description = "域名", required = false)
    private String domainName;
}
