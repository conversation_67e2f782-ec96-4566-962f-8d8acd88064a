package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: TODO
 * @Author: cher
 * @Date: 2023/2/17 15:05
 **/
@Data
public class SwitchFrontAppTypeCompensateReq implements Serializable {

    private static final long serialVersionUID = 1L;


    @Auth(type = AuthTypeConstant.APPLICATION)
    @ZsmpModelProperty(description = "应用编码", required = true)
    private String code;

    @ZsmpModelProperty(description = "需要同步实例历史上线记录的实例编码", required = true)
    private String instanceCode;

    @ZsmpModelProperty(description = "软连到default目录的commit")
    private String defaultCommit;
}
