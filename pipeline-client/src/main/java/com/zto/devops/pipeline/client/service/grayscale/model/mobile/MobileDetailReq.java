package com.zto.devops.pipeline.client.service.grayscale.model.mobile;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author：ch<PERSON><PERSON>yu
 * @Description：
 * @Date：2025/7/8 13:44
 */
@Data
@ZsmpModel(description = "移动端(安卓、ios等)详情req")
public class MobileDetailReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "版本id", sample = "1", required = true)
    private Long versionId;

    @ZsmpModelProperty(description = "环境", sample = "FAT", required = true)
    private String env;

    @ZsmpModelProperty(description = "产品code", sample = "FAT", required = true)
    private String productCode;
}
