package com.zto.devops.pipeline.client.service.namespace;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * namespace page req
 *
 * <AUTHOR>
 * @date 2023-03-13 13:37
 */
@Getter
@Setter
public class PageNamespaceReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 1L;
    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;
    @GatewayModelProperty(description = "名称", required = false)
    private String name;
    @GatewayModelProperty(description = "环境", required = false)
    private List<EnvEnum> env;

    @ZsmpModelProperty(description = "应用类型id", required = false)
    private List<String> applicationTypeCode;
    @ZsmpModelProperty(description = "应用编码", required = false)
    private String appCode;
}
