package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * H5VersionUpReq
 *
 * <AUTHOR>
 * @date 2025-06-05 10:15
 */
@Data
@ZsmpModel(description = "H5版本上线req")
public class H5VersionUpReq implements Serializable {
    @Auth(type = AuthTypeConstant.PRODUCT)
    @ZsmpModelProperty(description = "产品编码", sample = "product", required = true)
    private String productCode;

    @ZsmpModelProperty(description = "id", sample = "1", required = true)
    private Long id;


}
