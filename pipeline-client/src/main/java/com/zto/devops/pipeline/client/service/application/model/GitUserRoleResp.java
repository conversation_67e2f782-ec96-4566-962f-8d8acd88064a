package com.zto.devops.pipeline.client.service.application.model;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @Date: 2023/3/7 9:14
 */
@Data
@NoArgsConstructor
@ZsmpModel(description = "git用户角色VO")
public class GitUserRoleResp implements Serializable {
    @ZsmpModelProperty(description = "权限名称", sample = "1", required = false)
    private String name;

    @ZsmpModelProperty(description = "权限级别", sample = "1", required = false)
    public Integer value;

    public GitUserRoleResp(String name, Integer value) {
        this.name = name;
        this.value = value;
    }
}
