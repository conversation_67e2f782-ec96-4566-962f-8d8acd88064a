package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PageReleasePlanLogReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 9013975371127627923L;

    @GatewayModelProperty(description = "版本编码")
    private String versionCode;

    @GatewayModelProperty(description = "快照编码",required = false)
    private String releasePlanSnapshotCode;
}
