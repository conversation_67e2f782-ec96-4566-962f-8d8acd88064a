package com.zto.devops.pipeline.client.service.release.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MoveDeployBatchParam implements Serializable {
    @ZsmpModelProperty(description = "批次号", required = true)
    private Integer deployBatch;

    @ZsmpModelProperty(description = "历史批次号", required = false)
    private Integer oldDeployBatch;

    @ZsmpModelProperty(description = "事件编码", required = true)
    private List<String> eventCodes;
}
