package com.zto.devops.pipeline.client.service.configset.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@GatewayModel(description = "新增key的req")
public class AddKeyReq implements Serializable {

    @Auth(type = AuthTypeConstant.PRODUCT)
    @NotBlank
    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @NotBlank
    @GatewayModelProperty(description = "环境。开发:DEV,测试:FAT,预发:PRE,生产:PRO",sample = "FAT")
    private EnvEnum env;

    @NotBlank
    @GatewayModelProperty(description = "应用编码")
    private String applicationCode;

    @NotBlank
    @Size(min = 1, max = 60)
    @GatewayModelProperty(description = "配置集key。长度1~60")
    private String configSetKey;

    @NotNull
    @GatewayModelProperty(description = "配置集类型：1字符串，2数字，3布尔",sample = "1")
    private Integer configSetType;

    @Size(max = 200)
    @GatewayModelProperty(description = "key描述。长度0~200")
    private String keyDescription;

    @GatewayModelProperty(description = "规则的值")
    private String configSetValue;

    @NotNull
    @GatewayModelProperty(description = "类型1-灰度，2-全网",sample = "1")
    private Integer grayType;

    @Size(max = 100)
    @GatewayModelProperty(description = "百分比,0~100，整数", sample = "1", required = false)
    private Integer grayRatio;

    @GatewayModelProperty(description = "规则脚本",required = false)
    private String grayScript;

    @GatewayModelProperty(description = "uiData",required = false)
    private String uiData;

    @GatewayModelProperty(description = "描述。长度0~500",required = false)
    private String valueDescription;

}
