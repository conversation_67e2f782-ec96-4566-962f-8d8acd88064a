package com.zto.devops.pipeline.client.constants;

/**
 * zmas constant
 *
 * <AUTHOR>
 * @date 2025-06-30 10:38
 */
public class ZmasConstant {

    public static final String ZMAS_MOBILE_APP_BASIC_INFO = "zmasMobileAppBasicInfo";

    /**
     * 移动端灰度发布
     */
    public static final String ZMAS_MOBILE_VERSION_PUBLISH_URL = "/open/api/version/devtool/mobile/create";

    /**
     * 移动端rn灰度发布
     */
    public static final String ZMAS_MOBILE_VERSION_RN_PUBLISH_URL = "/open/api/version/rn/publish";

    /**
     * zmas app 列表查询
     */
    public static final String ZMAS_APP_LIST_QUERY_URL = "/open/api/app/appListQuery";

    /**
     * 扩展字段，bundle id，ios/android共用
     */
    public static final String ZMAS_KEY_BUNDLE_ID = "bundleId";

    /**
     * 扩展字段, 多语言
     */
    public static final String ZMAS_KEY_INTERNATIONAL_NAME = "internationalAppName";

    /**
     * 扩展字段，下载页/安装包
     */
    public static final String ZMAS_KEY_DOWNLOAD_TYPE = "downLoadType";

    /**
     * 扩展字段，文件下载地址
     */
    public static final String ZMAS_KEY_DOWNLOAD_URL = "downloadUrl";

    /**
     * 扩展字段，更新方式，见ZmasUpgradeTypeEnum
     */
    public static final String ZMAS_KEY_UPDATE_TYPE = "updateType";

    public static final String ZMAS_KEY_PRE_LOAD = "preLoad";

    public static final String ZMAS_KEY_RN_ANDROID_URL = "androidUrl";
    public static final String ZMAS_KEY_RN_ANDROID_SIZE = "androidSize";
    public static final String ZMAS_KEY_RN_ANDROID_MD5="AndroidMd5";

    public static final String ZMAS_KEY_RN_IOS_URL = "iosUrl";
    public static final String ZMAS_KEY_RN_IOS_SIZE = "iosSize";
    public static final String ZMAS_KEY_RN_IOS_MD5="IosMd5";

    public static final String ZMAS_KEY_RN_H5_ZIP_SIZE = "h5zipSize";
    public static final String ZMAS_KEY_RN_H5_ZIP_URL = "h5zipUrl";
    public static final String ZMAS_KEY_RN_H5_ZIP_MD5 = "h5zipMd5";

    public static final String ZMAS_KEY_RN_H5_SOURCEMAP_SIZE = "h5sourceMapSize";
    public static final String ZMAS_KEY_RN_H5_SOURCEMAP_URL = "h5sourceMapUrl";
    public static final String ZMAS_KEY_RN_H5_SOURCEMAP_MD5 = "h5sourceMapMd5";

    public static final String ZMAS_KEY_RN_HARMONY_URL = "harmonyUrl";
    public static final String ZMAS_KEY_RN_HARMONY_SIZE = "harmonySize";
    public static final String ZMAS_KEY_RN_HARMONY_MD5 = "harmonyMd5";

    public static final String ZMAS_KEY_RN_HARMONY_MAP_URL = "harmonyMapUrl";
    public static final String ZMAS_KEY_RN_HARMONY_MAP_SIZE = "harmonyMapSize";
    public static final String ZMAS_KEY_RN_HARMONY_MAP_MD5 = "harmonyMapMd5";

    public static final String ZMAS_KEY_RN_ANDROID_JS_URL = "androidJSUrl";
    public static final String ZMAS_KEY_RN_ANDROID_JS_SIZE = "androidJSSize";
    public static final String ZMAS_KEY_RN_ANDROID_JS_MD5 = "androidJSMd5";

    public static final String ZMAS_KEY_DESKTOP_ELECTRON_BUILD_PLATFORM = "electronBuildPlatform";

    /**
     * zmas 应用构建配置查询URL
     */
    public static final String ZMAS_APP_BUILD_CONFIG_URL = "/build_config/query?appKey=";
}
