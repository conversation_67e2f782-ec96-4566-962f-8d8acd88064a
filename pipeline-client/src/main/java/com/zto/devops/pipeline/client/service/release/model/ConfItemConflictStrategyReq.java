package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.pipeline.client.model.release.enums.ApolloConflictStrategyEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ConfItemConflictStrategyReq implements Serializable {

    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "计划code")
    private String releasePlanCode;

    @GatewayModelProperty(description = "发布事件code")
    private String releaseEventCode;

    @GatewayModelProperty(description = "事件code")
    private String eventCode;

    @GatewayModelProperty(description = "配置key")
    private String key;
    // 上线计划执行时，配置发生了冲突，执行策略（默认报错）：
    // 1：忽略，以命名空间里的为准
    // 2、覆盖，以上线计划为准
    @GatewayModelProperty(description = "配置操作")
    private ApolloConflictStrategyEnum strategy;

    @GatewayModelProperty(description = "生产环境当前生效值",required = false)
    private String releaseValue;
}
