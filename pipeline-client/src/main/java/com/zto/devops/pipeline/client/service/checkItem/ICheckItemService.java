package com.zto.devops.pipeline.client.service.checkItem;

import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.service.checkItem.model.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/30
 */
public interface ICheckItemService {

    /**
     * 添加检查项
     *
     * @param req
     * @return
     */
    Result<Void> add(AddCheckItemReq req);

    /**
     * 检查项列表
     *
     * @param req
     * @return
     */
    Result<List<CheckItemListResp>> list(QueryCheckItemReq req);

    /**
     * 查询检查项
     *
     * @param req
     * @return
     */
    Result<CheckItemDetailResp> detail(CheckItemCodeReq req);

    /**
     * 编辑检查项
     *
     * @param req
     * @return
     */
    Result<Void> edit(EditCheckItemReq req);

    /**
     * 删除检查项
     *
     * @param req
     * @return
     */
    Result<Void> delete(CheckItemCodeReq req);

    /**
     * 更改检查项状态
     *
     * @param req
     * @return
     */
    Result<Void> updateStatus(UpdateCheckItemStatusReq req);

    /**
     * 查询检查项规则
     *
     * @return
     */
    Result<List<EnumResp>> queryCheckRule();

    /**
     * 查询检查项触发动作
     *
     * @return
     */
    Result<List<EnumResp>> queryCheckAction();

    /**
     * 下发事项
     *
     * @param req
     * @return
     */
    Result<Void> issuedRecord(CheckItemCodeReq req);

    /**
     * 查询检查类型
     *
     * @return
     */
    Result<List<EnumResp>> queryCheckItemType();

    /**
     * 查询下发事项类型
     *
     * @return
     */
    Result<List<EnumResp>> queryCheckRecordType();

    /**
     * 查询事项接受角色
     *
     * @return
     */
    Result<List<EnumResp>> queryRecordAcceptRole();
}
