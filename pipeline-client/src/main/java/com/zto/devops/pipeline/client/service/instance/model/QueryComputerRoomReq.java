package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description: 查询机房
 * @Author: cher
 * @Date: 2022/1/10 11:06
 **/
@Data
@GatewayModel(description = "查询机房实体")
public class QueryComputerRoomReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "环境")
    @NotNull(message = "环境不能为空")
    private EnvEnum env;

    @GatewayModelProperty(description = "产品编码")
    @NotNull(message = "产品编码不能为空")
    @Auth(type = AuthTypeConstant.PRODUCT)
    private String productCode;

    /**
     * 主机类型 0: 虚拟机  1：物理机
     */
    @GatewayModelProperty(description = "主机类型 0: 虚拟机  1：物理机", required = false)
    private Integer instance_class;
}
