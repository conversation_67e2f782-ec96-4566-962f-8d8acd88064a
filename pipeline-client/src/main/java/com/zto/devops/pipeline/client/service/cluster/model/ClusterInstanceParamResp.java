package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date Created in 2022/8/12
 */
@Data
public class ClusterInstanceParamResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "是否加粗加红")
    private Boolean  boldAndRed;

    @GatewayModelProperty(description = "提示")
    private String message;
}
