package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @Description: 查询中通云主机来源入参
 * @Author: cher
 * @Date: 2022/1/6 13:33
 **/
@Data
@GatewayModel(description = "查询中通云主机来源实体")
public class QueryZtoCloudSourceReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "环境")
    @NotBlank(message = "环境不能为空")
    private EnvEnum env;

    /**
     * 主机类型 0: 虚拟机  1：物理机
     */
    @GatewayModelProperty(description = "主机类型 0: 虚拟机  1：物理机")
    @NotBlank(message = "主机类型不能为空")
    private Integer instance_class;
}
