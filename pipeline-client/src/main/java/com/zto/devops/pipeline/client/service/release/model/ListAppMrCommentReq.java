package com.zto.devops.pipeline.client.service.release.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ListAppMrCommentReq implements Serializable {
    @GatewayModelProperty(description = "版本编码")
    private String versionCode;

    @GatewayModelProperty(description = "应用编码")
    private String appId;

    @GatewayModelProperty(description = "开始时间", required = false)
    private Date startTime;

    @GatewayModelProperty(description = "结束时间", required = false)
    private Date endTime;

    @GatewayModelProperty(description = "评论人", required = false)
    private List<Long> commentatorId;
}
