package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/3
 * @Version 1.0
 */
@Data
public class StopInstancePreCheckResp implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * dubbo服务依赖方应用ID
     */
    private List<String> dubboServiceDependPartyAppId;

    /**
     * 流量监控时长, 24小时
     */
    private Integer trafficMonitoringDuration;

    /**
     * 流量访问类型, HTTP服务、Dubbo服务、MQ消费、JOB调度
     */
    private List<String> trafficAccessType;

    /**
     * 可停止的
     */
    private Boolean stoppable;
}
