package com.zto.devops.pipeline.client.service.pinpoint.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * pinpoint task base info req
 *
 * <AUTHOR>
 * @date 2024-07-03 16:13
 */
@Data
public class PinpointTaskBaseInfoReq implements Serializable {
    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "projectId")
    private String projectId;

    @GatewayModelProperty(description = "reportId")
    private String reportId;
}
