package com.zto.devops.pipeline.client.constants;

/**
 * jvm args constant
 *
 * <AUTHOR>
 * @date 2024-11-07 15:48
 */
public class JvmArgsConstant {
    /**
     * 用于base实例zcat标签上报
     */
    public static final String ZCAT_AGENT_BASE_TAG = "-Dzcat.agent.tag.namespaceTag=base";

    /**
     * gc.log的参数配置，在部署的时候需要替换成对应的appid
     */
    public static final String GC_LOG_JVM_ARGS = "-Xloggc:/dev/shm/gclog.@{appid}.log";

    /**
     * 用于字符串替换的模板
     */
    public static final String GC_LOG_JVM_ARGS_FORMAT = "-Xloggc:/dev/shm/gclog.%s.log";
}
