package com.zto.devops.pipeline.client.service.instance;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.common.model.GeneralResp;
import com.zto.devops.pipeline.client.model.instance.entity.InstanceFixIpVO;
import com.zto.devops.pipeline.client.model.instance.entity.InstancePodVO;
import com.zto.devops.pipeline.client.model.instance.entity.InstanceVO;
import com.zto.devops.pipeline.client.service.cluster.model.EditZkeClusterReq;
import com.zto.devops.pipeline.client.service.instance.model.*;
import com.zto.devops.pipeline.client.service.req.AddProductTagReq;
import com.zto.devops.pipeline.client.service.req.DeleteProductTagReq;
import com.zto.devops.pipeline.client.service.req.QueryProductTagReq;

import java.util.List;

/**
 * @Author: chenguangyu
 * @Description:
 * @Date: 2023/3/17 13:48
 */
public interface IInstanceService {


    /**
     * 获取实例详情
     *
     * @param req
     * @return
     */
    Result<InstanceResp> getInstanceDetail(InstanceCodeReq req);

    /**
     * 分页获取实例副本
     *
     * @param req
     * @return
     */
    PageResult<InstancePodVO> pageInstancePod(PageInstancePodReq req);

    /**
     * 分页获取实例固定ip池
     *
     * @param req
     * @return
     */
    PageResult<InstanceFixIpVO> pageInstanceFixedIp(PageInstanceFixedIpReq req);

    /**
     * 查询机房
     *
     * @param req
     * @return
     */
    Result<List<GeneralResp>> queryComputerRoom(QueryComputerRoomReq req);

    /**
     * 查询中通云主机镜像
     *
     * @param req
     * @return
     */
    Result<QueryZtoCloudImageResp> queryZtoCloudImage(QueryZtoCloudImageReq req);

    /**
     * 查询中通云主机来源
     *
     * @param req
     * @return
     */
    Result<QueryZtoCloudSourceResp> queryZtoCloudSource(QueryZtoCloudSourceReq req);

    /**
     * 搜索实例
     *
     * @param req
     * @return
     */
    Result<List<InstanceVO>> searchInstance(SearchInstanceReq req);

//    Result<List<GeneralResp>> getInstanceOperation(InstanceCodeReq req);

    Result<LogUrlResp> getLogUrl(LogUrlReq req);

    Result<PreCheckResp> instanceOperatePreCheck(InstanceCodesReq req);

    /**
     * 更改实例的空间
     *
     * @param req req
     * @return void
     */
    Result<Void> changeNamespace(InstanceNamespaceReq req);

    /**
     * 编辑实例容器副本
     *
     * @param req req
     * @return void
     */
    Result<Void> editZkeCluster(EditZkeClusterReq req);

    /**
     * 编辑实例容器副本前置校验-固定ip实例
     * @param req req
     * @return true/false
     */
    Result<Boolean> editZkeClusterPreCheck(EditZkeClusterReq req);

    /**
     * 初始化ng配置
     *
     * @param initNginxReq init ng req
     * @return void
     */
    Result<Void> initNginx(InitNginxReq initNginxReq);

    /**
     * restart instance
     *
     * @param req req
     * @return void
     */
    Result<Void> restartInstance(RestartInstanceReq req);

    /**
     * rollback instance
     *
     * @param req req
     * @return void
     */
    Result<Void> rollbackInstance(RollbackInstanceReq req);

    /**
     * stop instance pre check
     *
     * @param req req
     * @return void
     */
    Result<StopInstancePreCheckResp> stopInstancePreCheck(InstanceCodesReq req);

    /**
     * stop instance
     *
     * @param req req
     * @return void
     */
    Result<Void> stopInstance(StopInstanceReq req);

    /**
     * 编辑实例
     *
     * @param req req
     * @return void
     */
    Result<Void> editInstance(EditInstanceReq req);

    /**
     * 删除实例
     *
     * @param req req
     * @return void
     */
    Result<Void> removeInstance(RemoveInstanceReq req);

    /**
     * 健康检查
     *
     * @param req req
     * @return void
     */
    Result<Void> healthCheckInstance(HealthCheckInstanceReq req);

    /**
     * 添加标签
     *
     * @param req req
     * @return string
     */
    Result<String> addTag(AddProductTagReq req);

    /**
     * get tag
     *
     * @param req req
     * @return list
     */
    Result<List<GeneralResp>> getTag(QueryProductTagReq req);

    /**
     * 删除标签
     *
     * @param req req
     * @return void
     */
    Result<Void> deleteTag(DeleteProductTagReq req);

    Result<Void> syncInstanceToCmdb();

    /**
     * 分页查询实例
     *
     * @param req req
     * @return list
     */
    PageResult<PageInstanceResp> pageInstanceByCluster(PageInstanceReq req);

    /**
     * 分页查询应用实例视图的实例
     *
     * @param req req
     * @return list
     */
    PageResult<PageInstanceResp> pageInstanceForAppInstanceView(PageInstanceForAppInstanceViewReq req);

    /**
     * 分页查询应用IP视图的实例
     *
     * @param req req
     * @return list
     */
    PageResult<PageInstanceResp> pageInstanceForAppIpView(PageInstanceForAppIpViewReq req);

    /**
     * 切换网络隔离
     * */
    Result<Void> changeInstanceNetwork(ChangeInstanceNetworkReq req);

    /**
     * 多集群扩容时，校验多个集群的副本数是否一致，不一致抛出原因
     * @param req req
     * @return result
     */
    Result<EditZkeMultiClusterPreCheckResp> editZkeMultiClusterPreCheck(EditZkeMultiClusterPreCheckReq req);
}
