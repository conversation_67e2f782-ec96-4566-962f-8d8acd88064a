package com.zto.devops.pipeline.client.service.pipeline.query;

import com.zto.devops.pipeline.client.enums.BranchEnum;
import com.zto.devops.pipeline.client.enums.PlanStatusEnum;
import com.zto.devops.pipeline.client.enums.TaskTargetEnum;
import com.zto.devops.pipeline.client.enums.TaskTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class PipelineExecutionQuery implements Serializable {
    private String executionConfigCode;

    private String productCode;

    private TaskTargetEnum targetType;

    private String targetCode;

    private PlanStatusEnum status;

    private List<TaskTypeEnum> excludeTaskList;

    private Date startTime;

    private Date endTime;

    private List<TaskTypeEnum> taskTypeList;

    private BranchEnum branch;

    private Integer pageNum;

    private Integer pageSize;
}
