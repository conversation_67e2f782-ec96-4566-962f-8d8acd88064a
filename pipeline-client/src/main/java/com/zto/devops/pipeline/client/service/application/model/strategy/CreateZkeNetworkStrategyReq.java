package com.zto.devops.pipeline.client.service.application.model.strategy;

import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @Date: 2024/7/17 15:26
 */
@Data
@ZsmpModel(description = "申请应用网络策略Req")
public class CreateZkeNetworkStrategyReq implements Serializable {
    @ZsmpModelProperty(description = "应用编码", sample = "1", required = false)
    private List<String> applicationCodes;
    @ZsmpModelProperty(description = "zke策略编码(名称)是否为空。默认是空的", sample = "1", required = false)
    private Boolean zkeStrategyCodeEmpty = true;
    @ZsmpModelProperty(description = "最大处理条数。默认1000", sample = "1", required = false)
    private Integer maxDealCount = 100;
}
