package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.basicconfig.CoreApplicationIsolationModelEnum;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @Author: chenguangyu
 * @Description:
 * @Date: 2024/11/22 16:04
 */
@Data
@GatewayModel(description = "切换实例网络隔离实体")
public class ChangeInstanceNetworkReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实例编码
     */
    @Auth(type = AuthTypeConstant.INSTANCE)
    @GatewayModelProperty(description = "实例编码")
    @NotBlank(message = "实例编码不能为空")
    private String code;

    @GatewayModelProperty(description = "网络模式.上报模式:CHECK;拦截模式:INTERCEPT", sample = "")
    @NotBlank(message = "网络模式不能为空")
    private CoreApplicationIsolationModelEnum networkType;
}
