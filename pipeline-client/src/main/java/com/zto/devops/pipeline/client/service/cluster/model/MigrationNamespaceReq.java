package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

/**
 * @Description: 迁移空间
 * @Author: cher
 * @Date: 2021/12/6 16:01
 **/
@Data
public class MigrationNamespaceReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.NAMESPACE)
    @GatewayModelProperty(description = "迁移前的空间编码")
    @NotBlank(message = "迁移前的空间编码不能为空")
    private String fromNamespaceCode;

    @GatewayModelProperty(description = "迁移后的空间编码")
    @NotBlank(message = "迁移后的空间编码不能为空")
    private String toNamespaceCode;
}
