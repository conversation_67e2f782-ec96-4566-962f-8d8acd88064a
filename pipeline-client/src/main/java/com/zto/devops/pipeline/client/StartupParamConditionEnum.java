package com.zto.devops.pipeline.client;

/**
 * StartupParamConditionEnum
 *
 * <AUTHOR>
 * @date 2025-04-11 16:40
 **/
public enum StartupParamConditionEnum {
    TENANT_CODE("tenant_code", "租户", true),
    PRODUCT_LINE_CODE("product_line_code", "产品线", true),
    PRODUCT_CODE("product_code", "产品", true),
    APPID("appid", "应用", true),
    ENV("env", "环境", true),
    NAMESPACE_TYPE("namespace_type", "空间类型", true),
    APPLICATION_TYPE("application_type", "应用类型", true),
    CORE_APPLICATION_ISOLATION_MODEL("core_application_isolation_model", "核心应用-网络模式", true),

    IS_CORE_APPLICATION("is_core_application", "是否核心应用", false),
    IS_CORE_INSTANCE("is_core_instance", "是否核心实例", false),
    WARMUP_SUPPORT("warmup_support", "是否支持预热", false),
    ;

    final String value;
    final String desc;
    final boolean stringType;

    StartupParamConditionEnum(String value, String desc, boolean stringType) {
        this.value = value;
        this.desc = desc;
        this.stringType = stringType;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public boolean isStringType() {
        return stringType;
    }
}