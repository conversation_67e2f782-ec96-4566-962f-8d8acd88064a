package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2022/8/27
 * @Version 1.0
 */
@Data
public class QueryAreaInfoReq implements Serializable {

    private static final long serialVersionUID = -1L;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "父code", required = false)
    private String parentCode;
}
