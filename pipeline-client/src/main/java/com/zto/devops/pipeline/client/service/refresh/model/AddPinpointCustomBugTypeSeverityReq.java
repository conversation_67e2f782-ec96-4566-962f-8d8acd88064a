package com.zto.devops.pipeline.client.service.refresh.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 新增pinpoint自定义缺陷类型等级
 * @Author: cher
 * @Date: 2023/10/25 14:32
 **/
@Data
@ZsmpModel(description = "新增pinpoint自定义缺陷类型等级")
public class AddPinpointCustomBugTypeSeverityReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "中文缺陷类型", sample = "", required = false)
    private String  bugTypeCn;
    @Auth(type = AuthTypeConstant.SUPPER)
    @ZsmpModelProperty(description = "缺陷类型ID", sample = "", required = true)
    private String bugTypeId;
    @ZsmpModelProperty(description = "缺陷类型", sample = "", required = true)
    private String bugType;
    @ZsmpModelProperty(description = "缺陷等级", sample = "", required = true)
    private Integer severity;// 缺陷严重程度
    @ZsmpModelProperty(description = "源缺陷等级", sample = "", required = true)
    private Integer sourceSeverity;// 源缺陷严重程度
}
