package com.zto.devops.pipeline.client.service.release.model;

import com.zto.devops.pipeline.client.service.req.DeployStrategyReq;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


@GatewayModel(description = "查询上线计划部署批次")
@Setter
@Getter
public class ListDeployBatchReq extends DeployStrategyReq implements Serializable {

    @GatewayModelProperty(description = "版本code")
    private String versionCode;

    @GatewayModelProperty(description = "是否预发批次")
    private boolean preFlag;
}
