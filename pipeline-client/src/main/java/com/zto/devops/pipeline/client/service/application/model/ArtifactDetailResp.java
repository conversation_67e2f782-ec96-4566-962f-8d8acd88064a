package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.pipeline.client.enums.ArtifactTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date Created in 2021/8/13
 */
@Data
public class ArtifactDetailResp implements Serializable {

    /**
     * 流程（版本）编码
     */
    private String flowCode;

    /**
     * 流程（版本）名称
     */
    private String flowName;

    /**
     * 制品编码
     */
    private String code;

    /**
     * 制品名称
     */
    private String name;

    /**
     * commit ID
     */
    private String commit;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 8位commit id
     */
    private String shortCommit;

    /**
     * commit 地址
     */
    private String commitUrl;

    /**
     * git 地址
     */
    private String webUrl;

    /**
     * 来源
     */
    private ArtifactTypeEnum type;

    private String typeDesc;

    /**
     * 制品备注
     */
    private String remark;

    /**
     * 分支名称
     */
    private String branchName;

    /**
     * 分支类型
     */
    private String branchType;

    /**
     * 应用名称
     */
    private String applicationName;
}
