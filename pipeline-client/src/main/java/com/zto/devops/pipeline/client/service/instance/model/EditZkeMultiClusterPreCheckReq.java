package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.model.cluster.entity.WayneClusterVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * edit zke cluster pre check req
 *
 * <AUTHOR>
 * @date 2025-03-20 09:55
 */
@Data
public class EditZkeMultiClusterPreCheckReq implements Serializable {
    @Auth(type = AuthTypeConstant.INSTANCE)
    @GatewayModelProperty(description = "实例code")
    private String code;

    /**
     * 容器集群信息
     */
    @GatewayModelProperty(description = "集群信息")
    private WayneClusterVO cluster;
}
