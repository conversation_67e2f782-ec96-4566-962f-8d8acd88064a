package com.zto.devops.pipeline.client.service.refresh.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 新增pinpoint自定义缺陷类型等级
 * @Author: cher
 * @Date: 2023/10/25 14:32
 **/
@Data
@ZsmpModel(description = "新增pinpoint自定义缺陷类型等级")
public class DeletePinpointCustomBugTypeSeverityReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.SUPPER)
    @ZsmpModelProperty(description = "pinpoint自定义缺陷类型等级ID", sample = "", required = true)
    private Long id;
}
