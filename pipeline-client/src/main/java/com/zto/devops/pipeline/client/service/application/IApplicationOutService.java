package com.zto.devops.pipeline.client.service.application;

import com.zto.devops.pipeline.client.service.application.model.AppDeployTimeResp;

/**
 * application out service
 *
 * <AUTHOR>
 * @date 2023-04-07 10:41
 **/
public interface IApplicationOutService {

    /**
     * 对外-查询应用最近一次部署时间
     *
     * @param env   env
     * @param appId appId
     * @return result
     */
    com.zto.titans.common.entity.Result<AppDeployTimeResp> getAppLastDeployTime(String env, String appId);
}