package com.zto.devops.pipeline.client.service.startup.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * StatusChangeStartupParamRuleReq
 *
 * <AUTHOR>
 * @date 2025-04-17 15:00
 */
@Data
public class StatusChangeStartupParamRuleReq implements Serializable {
    @GatewayModelProperty(description = "启动参数编码")
    private String startupParamConfigCode;

    @GatewayModelProperty(description = "启动参数状态")
    private Boolean status;
}
