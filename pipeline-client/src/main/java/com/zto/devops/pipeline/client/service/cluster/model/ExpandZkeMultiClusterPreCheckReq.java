package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.MachineTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * ExpandZkeMultiClusterPreCheckReq
 *
 * <AUTHOR>
 * @date 2025-03-20 10:18
 */
@Data
public class ExpandZkeMultiClusterPreCheckReq implements Serializable {
    @Auth(type = AuthTypeConstant.CLUSTER)
    @GatewayModelProperty(description = "集群code")
    private String code;

    @GatewayModelProperty(description = "扩容的机器类型")
    private MachineTypeEnum type;
}
