package com.zto.devops.pipeline.client.service.apiroute.model;

import com.zto.devops.pipeline.client.model.apiroute.entity.GatewayApiRouteInfoVO;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * gateway api route resp
 *
 * <AUTHOR>
 * @date 2024-10-23 15:21
 */
@Data
public class PageGatewayApiRouteResp implements Serializable {
    @GatewayModelProperty(description = "列表")
    private List<GatewayApiRouteInfoVO> gatewayApiRouteInfoList;

    @GatewayModelProperty(description = "总数")
    private Integer total;

    @GatewayModelProperty(description = "页号")
    private Integer page;

    @GatewayModelProperty(description = "页数")
    private Integer size;
}
