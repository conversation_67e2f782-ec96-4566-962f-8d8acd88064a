package com.zto.devops.pipeline.client.service.release.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/8
 * @Version 1.0
 */
@Data
public class ListExecutableEventsReq implements Serializable {

    @GatewayModelProperty(description = "可执行的父类编码")
    private String parentEventCode;

    @GatewayModelProperty(description = "版本编码")
    private String versionCode;

    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "上线计划快照编码", required = false)
    private String releasePlanSnapshotCode;
}
