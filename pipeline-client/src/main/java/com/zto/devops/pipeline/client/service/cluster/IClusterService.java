package com.zto.devops.pipeline.client.service.cluster;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.application.entity.ArtifactVO;
import com.zto.devops.pipeline.client.model.cluster.entity.ContainerNamespaceVO;
import com.zto.devops.pipeline.client.model.cluster.entity.WayneContainerVO;
import com.zto.devops.pipeline.client.model.engine.zke.WaynePodStatusDTO;
import com.zto.devops.pipeline.client.service.application.model.ClusterInstanceReq;
import com.zto.devops.pipeline.client.service.application.model.WaynePodRestartReq;
import com.zto.devops.pipeline.client.service.application.model.WaynePodStatusReq;
import com.zto.devops.pipeline.client.service.cluster.model.*;
import com.zto.devops.pipeline.client.service.req.ProductCodeOnlyReq;

import java.util.List;
import java.util.Map;

/**
 * @Description: TODO
 * @Author: cher
 * @Date: 2023/3/13 11:02
 **/
public interface IClusterService {

    /**
     * 集群列表
     *
     * @param req
     * @return
     */
    PageResult<PageClusterResp> listClusterQuery(PageClusterReq req);

    /**
     * 编辑集群
     *
     * @param req
     * @return
     */
    Result<Void> editCluster(EditClusterReq req);

    Result<Map<String, List<ArtifactVO>>> getArtifacts(ClusterCodeReq req);

    Result<PageClusterResp> clusterDetail(FindClusterByCodeReq req);

//    Result<List<GeneralResp>> getClusterOperation(ClusterCodeReq req);

    Result<WayneContainerVO> getNamespace(ContainerClusterReq req);

    Result<List<ClusterStatusResp>> getClusterStatus(ProductCodeOnlyReq req);

    Result<ClusterInstanceParamResp> checkInstanceParam(ClusterInstanceReq req);

    Result<List<ContainerNamespaceVO>> getContainerNamespace(ContainerNamespaceReq req);

    Result<ExpandPreCheckResp> expandPreCheck(ExpandPreCheckReq req);

    /**
     * 扩容前生产进程个数校验
     *
     * @param req
     * @return
     */
    Result<ExpandProPreCheckResp> expandProPreCheck(ExpandProPreCheckReq req);

    Result<WaynePodStatusDTO> getPodStatus(WaynePodStatusReq req);

    Result<Void> restartPod(WaynePodRestartReq req);

    /**
     * 集群扩容
     *
     * @param req
     * @return
     */
    Result<Void> expandCluster(ExpandClusterReq req);

    /**
     * 集群缩容
     *
     * @param req
     * @return
     */
    Result<Void> shrinkCluster(ShrinkClusterReq req);

    /**
     * 集群停止
     *
     * @param req
     * @return
     */
    Result<Void> stopCluster(ClusterOperateReq req);

    /**
     * 集群重启
     *
     * @param req
     * @return
     */
    Result<Void> restartCluster(ClusterOperateReq req);

    /**
     * 集群回滚
     *
     * @param req
     * @return
     */
    Result<Void> rollbackCluster(RollbackClusterReq req);

    /**
     * 集群迁移实例
     *
     * @param req
     * @return
     */
    Result<Void> instanceMigrate(InstanceMigrateReq req);

    /**
     * 新增容器实例时，涉及网关域名的appid校验提醒
     * @param req req
     * @return result
     */
    Result<ExpandZkeMultiClusterPreCheckResp> expandZkeMultiClusterPreCheck(ExpandZkeMultiClusterPreCheckReq req);
}
