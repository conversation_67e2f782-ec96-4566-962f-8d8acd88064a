package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import com.zto.zsmp.annotation.gateway.GatewayModel;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Description:
 * @Date: 2022/9/27 16:32
 */
@Data
@GatewayModel(description = "灰度分页查询req")
public class PageAllGrayscaleReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = -6641647012546445483L;
    @ZsmpModelProperty(description = "产品编码", sample = "1", required = false)
    private String productCode;
    @ZsmpModelProperty(description = "环境", sample = "1", required = false)
    private String env;
    @ZsmpModelProperty(description = "流程(版本)编码", sample = "1", required = false)
    private String flowCode;
    @ZsmpModelProperty(description = "灰度编码", sample = "1", required = false)
    private String code;
}
