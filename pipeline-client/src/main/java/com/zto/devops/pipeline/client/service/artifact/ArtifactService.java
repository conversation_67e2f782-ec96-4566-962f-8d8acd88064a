package com.zto.devops.pipeline.client.service.artifact;

import com.zto.devops.framework.client.dto.PageResult;
import com.zto.devops.framework.client.dto.Result;
import com.zto.devops.pipeline.client.model.application.entity.ArtifactVO;
import com.zto.devops.pipeline.client.service.application.model.CheckAppTitansVersionReq;
import com.zto.devops.pipeline.client.service.application.model.ListArtifactReq;
import com.zto.devops.pipeline.client.service.artifact.model.PageArtifactReq;

import java.util.List;

/**
 * artifact service
 *
 * <AUTHOR>
 * @date 2023-03-17 15:17
 **/
public interface ArtifactService {

    Result<List<ArtifactVO>> ListArtifactByClusterCode(ListArtifactReq req);

    PageResult<ArtifactVO> queryArtifactList(PageArtifactReq req);

    /**
     * @param req
     * @return 检查app titans版本
     */
    Result<String> checkAppTitansVersion(CheckAppTitansVersionReq req);
}