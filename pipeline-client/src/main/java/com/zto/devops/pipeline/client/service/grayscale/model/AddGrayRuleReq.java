package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2022/8/25
 * @Version 1.0
 */
@Data
public class AddGrayRuleReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;

    @GatewayModelProperty(description = "规则名称")
    private String name;

    @GatewayModelProperty(description = "规则")
    private String rules;

    @GatewayModelProperty(description = "javascript脚本")
    private String uiData;

    @GatewayModelProperty(description = "开始时间")
    private Date startTime;

    @GatewayModelProperty(description = "结束时间")
    private Date endTime;

    private String description;
}
