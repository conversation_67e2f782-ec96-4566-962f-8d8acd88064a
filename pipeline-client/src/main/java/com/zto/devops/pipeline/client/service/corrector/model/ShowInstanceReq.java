package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * show instance req
 *
 * <AUTHOR>
 * @date 2024-05-13 19:36
 */
@Data
public class ShowInstanceReq implements Serializable {
    @ZsmpModelProperty(description = "实例code", sample = "", required = true)
    private String instanceCode;

    /**
     * 只针对迁移到base的实例进行处理，条件为pp_instance.auto_env_flag=false
     */
    @ZsmpModelProperty(description = "是否展示实例", sample = "", required = true)
    private Boolean enable;
}
