package com.zto.devops.pipeline.client.service.grayscale.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.devops.pipeline.client.enums.GrayscaleEventEnum;
import com.zto.devops.pipeline.client.enums.GrayscaleTypeEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * CheckH5VersionOperateReq
 *
 * <AUTHOR>
 * @date 2025-06-06 16:34
 */
@Data
public class CheckH5VersionOperateReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @Auth(type = AuthTypeConstant.APP_ID)
    @ZsmpModelProperty(description = "应用id", sample = "APP1", required = true)
    private String appId;

    @ZsmpModelProperty(description = "环境", sample = "FAT", required = true)
    private EnvEnum env;

    @ZsmpModelProperty(description = "版本id", sample = "1", required = true)
    private Long versionId;

    @ZsmpModelProperty(description = "灰度类型", sample = "ALL", required = true)
    private GrayscaleTypeEnum type;

    @ZsmpModelProperty(description = "操作", sample = "ONLINE", required = true)
    private GrayscaleEventEnum operate;
}
