package com.zto.devops.pipeline.client.service.namespace.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * app migrate namespace req
 *
 * <AUTHOR>
 * @date 2023-03-16 13:21
 */
@Data
public class ApplicationNamespaceResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @GatewayModelProperty(description = "环境")
    private String env;

    @GatewayModelProperty(description = "当前空间编码")
    private String namespaceCode;

    @GatewayModelProperty(description = "当前空间名")
    private String namespaceName;

    @GatewayModelProperty(description = "空间类型")
    private String type;
}
