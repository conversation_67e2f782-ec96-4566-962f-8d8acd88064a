package com.zto.devops.pipeline.client.service.instance.model;

import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PageInstanceForAppInstanceViewReq extends PageQueryBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "环境", required = true)
    private String env;
    @ZsmpModelProperty(description = "应用code", required = true)
    private String applicationCode;
    @ZsmpModelProperty(description = "实例名称")
    private String instanceName;
    @ZsmpModelProperty(description = "实例ip")
    private String instanceIp;
    @ZsmpModelProperty(description = "实例标签")
    private String instanceTag;
    @ZsmpModelProperty(description = "实例编码集合")
    private List<String> instanceCodes;
    @ZsmpModelProperty(description = "空间名称")
    private String namespaceName;
}
