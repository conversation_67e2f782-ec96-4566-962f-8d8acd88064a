package com.zto.devops.pipeline.client.service.cluster.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.EnvEnum;
import com.zto.devops.pipeline.client.enums.NamespaceTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class PageNamespaceExtendReq extends PageQueryBase implements Serializable {
    private static final long serialVersionUID = 1L;
    @Auth(type = AuthTypeConstant.PRODUCT)
    @GatewayModelProperty(description = "产品编码")
    private String productCode;
    @GatewayModelProperty(description = "名称", required = false)
    private String name;
    @GatewayModelProperty(description = "环境", required = false)
    private List<EnvEnum> env;

    @GatewayModelProperty(description = "版本code", required = false)
    private String versionCode;

    @GatewayModelProperty(description = "空间类型", required = false)
    private List<NamespaceTypeEnum> namespaceTypeList;
}
