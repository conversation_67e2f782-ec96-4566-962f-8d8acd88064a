package com.zto.devops.pipeline.client.service.namespace.model;

import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SwitchNamespaceReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @GatewayModelProperty( description = "空间编码", sample = "空间编码")
    private String code;

    @GatewayModelProperty(description = "隐藏/打开")
    private Boolean enable;
}
