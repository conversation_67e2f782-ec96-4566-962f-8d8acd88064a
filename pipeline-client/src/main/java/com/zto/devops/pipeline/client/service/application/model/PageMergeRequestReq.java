package com.zto.devops.pipeline.client.service.application.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.framework.client.query.PageQueryBase;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.devops.pipeline.client.enums.FlowBusinessTypeEnum;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


@Data
public class PageMergeRequestReq extends PageQueryBase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 流程编码
     */
    @Auth(type = AuthTypeConstant.FLOW)
    @GatewayModelProperty(description = "流程编码")
    @NotBlank(message = "流程编码不能为空")
    private String flowCode;

    /**
     * 流程类型
     */
    @GatewayModelProperty(description = "流程类型")
    @NotNull(message = "流程类型不能为空")
    private FlowBusinessTypeEnum flowBusinessType;


    @GatewayModelProperty(description = "标题",required = false)
    private String title;

    @GatewayModelProperty(description = "应用code",required = false)
    private List<String> applicationCodeList;   //pp_flow_application下查询应用code 再去pp_application查询name  最终转为git_project_id查询

    @GatewayModelProperty(description = "代码仓库id",required = false)
    private List<Long> projectIdList;   //pp_flow_application下查询应用code  pp_application中查询git_project_id  pp_gitlab_project查询工程名称

    @GatewayModelProperty(description = "状态",required = false)
    private List<String> statusList;

    @GatewayModelProperty(description = "审核人",required = false)
    private List<Long> reviewerList;

    @GatewayModelProperty(description = "创建人",required = false)
    private List<Long> creatorList;
}
