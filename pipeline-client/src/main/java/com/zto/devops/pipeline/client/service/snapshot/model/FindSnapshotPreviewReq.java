package com.zto.devops.pipeline.client.service.snapshot.model;

import com.zto.devops.framework.client.auth.annotation.Auth;
import com.zto.devops.pipeline.client.constants.AuthTypeConstant;
import com.zto.zsmp.annotation.gateway.GatewayModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * find snapshot preview req
 *
 * <AUTHOR>
 * @date 2023-03-16 15:40
 */
@Getter
@Setter
public class FindSnapshotPreviewReq implements Serializable {
    private static final long serialVersionUID = 1L;
    @Auth(type = AuthTypeConstant.NAMESPACE)
    @GatewayModelProperty( description = "空间编码", sample = "NS202101010001")
    private String code;
}
