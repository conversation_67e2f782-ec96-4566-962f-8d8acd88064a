package com.zto.devops.pipeline.client.service.corrector.model;

import com.zto.devops.pipeline.client.enums.releaseWindow.ReleaseWindowTargetTypeEnum;
import com.zto.zsmp.annotation.ZsmpModel;
import com.zto.zsmp.annotation.ZsmpModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 特批产品列表出参
 * @Author: cher
 * @Date: 2022/9/26 16:16
 **/
@ZsmpModel(description = "特批产品列表出参")
@Data
public class SpecialProductListResp implements Serializable {
    private static final long serialVersionUID = 1L;

    @ZsmpModelProperty(description = "主键", sample = "")
    private String id;

    @ZsmpModelProperty(description = "对象编码", sample = "PRO_1")
    private String targetCode;

    @ZsmpModelProperty(description = "对象名称", sample = "PRO_1")
    private String targetName;

    @ZsmpModelProperty(description = "对象编码", sample = "PRO_1")
    private String productCode;

    @ZsmpModelProperty(description = "对象名称", sample = "PRO_1")
    private String productName;

    @ZsmpModelProperty(description = "特批发布开始时间", sample = "2020-11-11 00:00:00")
    private Date startTime;

    @ZsmpModelProperty(description = "特批发布结束时间", sample = "2020-11-11 23:59:59")
    private Date endTime;

    private ReleaseWindowTargetTypeEnum targetType;

    @ZsmpModelProperty(description = "对象类型", sample = "产品")
    private String targetTypeDesc;

    public String getTargetTypeDesc() {
        if (targetType != null) {
            return targetType.getDesc();
        }
        return null;
    }
}
